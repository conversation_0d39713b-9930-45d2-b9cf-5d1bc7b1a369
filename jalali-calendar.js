// کدهای مربوط به تقویم جلالی برای تاریخ اقدام بعدی
let currentJalaliDate = null;
let selectedJalaliDate = null;
let activeDateField = null; // فیلد فعال تاریخ که در حال ویرایش است

// تبدیل اعداد به فارسی
function toPersianDigits(n) {
    const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
    return String(n).replace(/\d/g, function(d) {
        return persianDigits[parseInt(d)];
    });
}

// نام ماه‌های جلالی
const jalaaliMonthNames = [
    'فروردین', 'اردیبهشت', 'خرداد', 'تیر', 'مرداد', 'شهریور',
    'مهر', 'آبان', 'آذر', 'دی', 'بهمن', 'اسفند'
];

// نمایش تقویم جلالی
function renderJalaliCalendar(year, month) {
    // نمایش ماه و سال فعلی
    const currentMonthYearElement = document.getElementById('currentMonthYear');
    currentMonthYearElement.textContent = `${jalaaliMonthNames[month - 1]} ${toPersianDigits(year)}`;
    
    // محاسبه تعداد روزهای ماه
    const daysInMonth = jalaali.jalaaliMonthLength(year, month);
    
    // محاسبه روز هفته اولین روز ماه (شنبه: 0، یکشنبه: 1، ...)
    const firstDayGregorian = jalaali.toGregorian(year, month, 1);
    const firstDayDate = new Date(firstDayGregorian.gy, firstDayGregorian.gm - 1, firstDayGregorian.gd);
    let firstDayOfWeek = (firstDayDate.getDay() + 1) % 7; // تبدیل به شنبه: 0، یکشنبه: 1، ...
    
    // ایجاد روزهای تقویم
    const calendarDaysElement = document.getElementById('calendarDays');
    calendarDaysElement.innerHTML = '';
    
    // اضافه کردن روزهای خالی برای هماهنگی با روز هفته
    for (let i = 0; i < firstDayOfWeek; i++) {
        const emptyDay = document.createElement('div');
        emptyDay.className = 'py-2';
        calendarDaysElement.appendChild(emptyDay);
    }
    
    // اضافه کردن روزهای ماه
    for (let day = 1; day <= daysInMonth; day++) {
        const dayElement = document.createElement('div');
        dayElement.className = 'py-2 text-center calendar-day cursor-pointer hover:bg-gray-100 rounded';
        dayElement.textContent = toPersianDigits(day);
        
        // بررسی اگر این روز مطابق با تاریخ انتخاب شده است
        if (selectedJalaliDate && selectedJalaliDate.jy === year && 
            selectedJalaliDate.jm === month && selectedJalaliDate.jd === day) {
            dayElement.classList.add('bg-blue-500', 'text-white', 'font-bold');
        }
        
        // بررسی اگر این روز، امروز است
        const todayGregorian = new Date();
        const todayJalali = jalaali.toJalaali(
            todayGregorian.getFullYear(),
            todayGregorian.getMonth() + 1,
            todayGregorian.getDate()
        );
        
        if (todayJalali.jy === year && todayJalali.jm === month && todayJalali.jd === day) {
            dayElement.classList.add('border', 'border-blue-500');
        }
        
        // اضافه کردن رویداد کلیک برای انتخاب روز
        dayElement.addEventListener('click', function() {
            selectDate(year, month, day);
        });
        
        calendarDaysElement.appendChild(dayElement);
    }
}

// انتخاب تاریخ
function selectDate(year, month, day) {
    selectedJalaliDate = { jy: year, jm: month, jd: day };
    
    // به‌روزرسانی نمایش تقویم
    renderJalaliCalendar(currentJalaliDate.jy, currentJalaliDate.jm);
    
    // بررسی اگر فیلد فعال برای فیلترها است
    if (activeDateField && activeDateField.dataset.dateField) {
        // نمایش تاریخ به صورت خوانا
        const formattedDate = `${toPersianDigits(selectedJalaliDate.jd)} ${jalaaliMonthNames[selectedJalaliDate.jm - 1]} ${toPersianDigits(selectedJalaliDate.jy)}`;
        activeDateField.value = formattedDate;
        
        // ذخیره تاریخ در data-date attribute به فرمت ISO
        const gregorianDate = jalaali.toGregorian(year, month, day);
        const isoDate = `${gregorianDate.gy}-${String(gregorianDate.gm).padStart(2, '0')}-${String(gregorianDate.gd).padStart(2, '0')}`;
        activeDateField.setAttribute('data-date', isoDate);
        
        // برای فیلترها، تاریخ را به فرمت yyyy/mm/dd ذخیره کنیم
        const jalaliDateFormatted = `${selectedJalaliDate.jy}/${String(selectedJalaliDate.jm).padStart(2, '0')}/${String(selectedJalaliDate.jd).padStart(2, '0')}`;
        activeDateField.setAttribute('data-jalali-date', jalaliDateFormatted);
        
        // فقط بستن پاپ آپ تقویم بدون تاثیر بر پنجره فیلتر
        document.getElementById('jalaliDatePickerModal').classList.add('hidden');
        return;
    }
    
    // ذخیره تاریخ انتخاب شده در localStorage برای فیلد اقدام بعدی پرونده (کد اصلی)
    localStorage.setItem(`nextActionDate_${getCurrentCaseId()}`, JSON.stringify(selectedJalaliDate));
    
    // به‌روزرسانی متن دکمه
    updateNextActionDateButton();
    
    // بستن پاپ آپ تقویم
    document.getElementById('jalaliDatePickerModal').classList.add('hidden');
}

// به‌روزرسانی متن دکمه تاریخ اقدام بعدی
function updateNextActionDateButton() {
    const dateDisplay = document.getElementById('nextActionDateDisplay');
    if (!dateDisplay) return; // اگر هنوز المان وجود ندارد، خارج شو
    
    // اگر selectedJalaliDate تعریف نشده باشد، سعی می‌کنیم آن را از localStorage بازیابی کنیم
    if (!selectedJalaliDate && getCurrentCaseId()) {
        const savedDateString = localStorage.getItem(`nextActionDate_${getCurrentCaseId()}`);
        if (savedDateString) {
            selectedJalaliDate = JSON.parse(savedDateString);
        }
    }
    
    if (selectedJalaliDate) {
        const formattedDate = `${toPersianDigits(selectedJalaliDate.jd)} ${jalaaliMonthNames[selectedJalaliDate.jm - 1]} ${toPersianDigits(selectedJalaliDate.jy)}`;
        dateDisplay.textContent = formattedDate;
        dateDisplay.classList.remove('text-gray-500');
        dateDisplay.classList.add('text-black', 'font-semibold');
    } else {
        dateDisplay.textContent = 'انتخاب تاریخ';
        dateDisplay.classList.add('text-gray-500');
        dateDisplay.classList.remove('text-black', 'font-semibold');
    }
}

// نمایش ماه قبل
function showPreviousMonth() {
    let newMonth = currentJalaliDate.jm - 1;
    let newYear = currentJalaliDate.jy;
    
    if (newMonth < 1) {
        newMonth = 12;
        newYear--;
    }
    
    currentJalaliDate = { jy: newYear, jm: newMonth, jd: 1 };
    renderJalaliCalendar(newYear, newMonth);
}

// نمایش ماه بعد
function showNextMonth() {
    let newMonth = currentJalaliDate.jm + 1;
    let newYear = currentJalaliDate.jy;
    
    if (newMonth > 12) {
        newMonth = 1;
        newYear++;
    }
    
    currentJalaliDate = { jy: newYear, jm: newMonth, jd: 1 };
    renderJalaliCalendar(newYear, newMonth);
}

// محاسبه تاریخ آینده با افزودن تعداد روز مشخص به روز جاری
function calculateFutureDate(daysToAdd) {
    // گرفتن تاریخ امروز
    const today = new Date();
    
    // ایجاد یک تاریخ جدید با افزودن تعداد روز
    const futureDate = new Date();
    futureDate.setDate(today.getDate() + parseInt(daysToAdd));
    
    // تبدیل به تاریخ جلالی
    const futureJalali = jalaali.toJalaali(
        futureDate.getFullYear(),
        futureDate.getMonth() + 1,
        futureDate.getDate()
    );
    
    // انتخاب تاریخ محاسبه شده
    selectDate(futureJalali.jy, futureJalali.jm, futureJalali.jd);
}

// اضافه کردن رویدادهای مربوط به تقویم جلالی
document.addEventListener('DOMContentLoaded', function() {
    // تابع برای تنظیم نمایش بخش "تعداد روز" بر اساس نوع فیلد
    function toggleDaysToAddSection() {
        const daysToAddContainer = document.getElementById('daysToAddContainer');
        if (!daysToAddContainer) return;
        
        // اگر فیلد فعال از نوع فیلتر است، بخش "تعداد روز" را مخفی کن
        if (activeDateField && activeDateField.dataset.dateField) {
            daysToAddContainer.style.display = 'none';
        } else {
            daysToAddContainer.style.display = 'block';
        }
    }
    
    // رویداد دکمه تاریخ اقدام بعدی
    const nextActionDateButton = document.getElementById('nextActionDateButton');
    if (nextActionDateButton) {
        nextActionDateButton.addEventListener('click', function() {
            activeDateField = null; // ریست کردن فیلد فعال
            
            // تنظیم تاریخ فعلی به عنوان تاریخ جاری تقویم
            const today = new Date();
            currentJalaliDate = jalaali.toJalaali(
                today.getFullYear(),
                today.getMonth() + 1,
                today.getDate()
            );
            
            // نمایش تقویم
            renderJalaliCalendar(currentJalaliDate.jy, currentJalaliDate.jm);
            
            // تنظیم نمایش بخش "تعداد روز"
            toggleDaysToAddSection();
            
            // نمایش پاپ آپ تقویم
            document.getElementById('jalaliDatePickerModal').classList.remove('hidden');
        });
    }
    
    // رویداد کلیک برای فیلدهای تاریخ فیلتر
    const dateFields = document.querySelectorAll('input[data-date-field]');
    dateFields.forEach(field => {
        field.addEventListener('click', function() {
            activeDateField = this; // تنظیم فیلد فعال
            
            // تنظیم تاریخ فعلی به عنوان تاریخ جاری تقویم
            const today = new Date();
            currentJalaliDate = jalaali.toJalaali(
                today.getFullYear(),
                today.getMonth() + 1,
                today.getDate()
            );
            
            // اگر این فیلد قبلاً تاریخی داشته، آن را انتخاب کنیم
            const existingDate = this.getAttribute('data-date');
            if (existingDate) {
                const parts = existingDate.split('-');
                const gregorianDate = {
                    gy: parseInt(parts[0]),
                    gm: parseInt(parts[1]),
                    gd: parseInt(parts[2])
                };
                
                const jalaliDate = jalaali.toJalaali(gregorianDate.gy, gregorianDate.gm, gregorianDate.gd);
                selectedJalaliDate = jalaliDate;
                currentJalaliDate = { jy: jalaliDate.jy, jm: jalaliDate.jm, jd: 1 };
            } else {
                selectedJalaliDate = null;
            }
            
            // نمایش تقویم
            renderJalaliCalendar(currentJalaliDate.jy, currentJalaliDate.jm);
            
            // تنظیم نمایش بخش "تعداد روز"
            toggleDaysToAddSection();
            
            // نمایش پاپ آپ تقویم
            document.getElementById('jalaliDatePickerModal').classList.remove('hidden');
        });
    });
    
    // جلوگیری از حباب‌سازی رویدادهای کلیک در تقویم برای جلوگیری از بسته شدن پنجره فیلتر
    const calendarModal = document.getElementById('jalaliDatePickerModal');
    if (calendarModal) {
        calendarModal.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
    
    // اضافه کردن رویداد برای جلوگیری از حباب‌سازی کلیک به همه المان‌های داخل تقویم
    const calendarDays = document.getElementById('calendarDays');
    if (calendarDays) {
        calendarDays.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
    
    // توقف حباب‌سازی برای همه دکمه‌های تقویم
    const calendarButtons = document.querySelectorAll('#jalaliDatePickerModal button');
    calendarButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });
    
    // رویداد فیلد تعداد روز
    const daysToAddInput = document.getElementById('daysToAdd');
    if (daysToAddInput) {
        // متغیر برای نگهداری مقدار واقعی (انگلیسی) فیلد
        let actualValue = '';
        
        // رویداد اینتر برای فیلد تعداد روز
        daysToAddInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                
                if (actualValue && !isNaN(actualValue) && parseInt(actualValue) > 0) {
                    calculateFutureDate(actualValue);
                    // پاک کردن مقدار ورودی پس از استفاده
                    daysToAddInput.value = '';
                    actualValue = '';
                } else {
                    // نمایش خطا به کاربر
                    alert('لطفاً یک عدد مثبت وارد کنید');
                }
            }
        });
        
        // استفاده از keydown برای کنترل ورودی
        daysToAddInput.addEventListener('keydown', function(event) {
            // اجازه دادن به کلیدهای کنترلی
            if (event.key === 'Backspace' || event.key === 'Delete' || 
                event.key === 'ArrowLeft' || event.key === 'ArrowRight' || 
                event.key === 'Tab' || event.key === 'Enter' ||
                event.ctrlKey || event.metaKey) {
                
                // اگر Backspace یا Delete است، باید مقدار واقعی نیز بروزرسانی شود
                if (event.key === 'Backspace') {
                    setTimeout(() => {
                        actualValue = daysToAddInput.value.replace(/[۰-۹]/g, d => '۰۱۲۳۴۵۶۷۸۹'.indexOf(d));
                    }, 10);
                }
                return;
            }
            
            // اجازه دادن به اعداد انگلیسی و تبدیل به فارسی
            if (event.key >= '0' && event.key <= '9') {
                event.preventDefault();
                const persianDigit = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'][parseInt(event.key)];
                
                const selectionStart = this.selectionStart;
                const selectionEnd = this.selectionEnd;
                
                // اضافه کردن عدد فارسی به نمایش
                this.value = this.value.slice(0, selectionStart) + persianDigit + this.value.slice(selectionEnd);
                
                // بروزرسانی مقدار واقعی با عدد انگلیسی
                actualValue = actualValue.slice(0, selectionStart) + event.key + actualValue.slice(selectionEnd);
                
                // تنظیم موقعیت مکان‌نما
                this.selectionStart = this.selectionEnd = selectionStart + 1;
                return;
            }
            
            // تبدیل اعداد فارسی به نمایش فارسی و مقدار انگلیسی
            const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
            if (persianDigits.includes(event.key)) {
                event.preventDefault();
                
                const englishDigit = persianDigits.indexOf(event.key).toString();
                const selectionStart = this.selectionStart;
                const selectionEnd = this.selectionEnd;
                
                // نمایش همان عدد فارسی
                this.value = this.value.slice(0, selectionStart) + event.key + this.value.slice(selectionEnd);
                
                // بروزرسانی مقدار واقعی با عدد انگلیسی
                actualValue = actualValue.slice(0, selectionStart) + englishDigit + actualValue.slice(selectionEnd);
                
                // تنظیم موقعیت مکان‌نما
                this.selectionStart = this.selectionEnd = selectionStart + 1;
            } else {
                // جلوگیری از ورود کاراکترهای غیر عددی
                event.preventDefault();
            }
        });
        
        // بروزرسانی مقدار واقعی با پیست کردن
        daysToAddInput.addEventListener('paste', function(event) {
            event.preventDefault();
            
            // دریافت متن کپی شده
            const pasteData = (event.clipboardData || window.clipboardData).getData('text');
            
            // بررسی اینکه آیا فقط عدد است
            if (!/^\d+$/.test(pasteData)) {
                alert('لطفاً فقط اعداد را پیست کنید');
                return;
            }
            
            // تبدیل اعداد انگلیسی به فارسی برای نمایش
            const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
            let persianText = '';
            for (let i = 0; i < pasteData.length; i++) {
                const digit = parseInt(pasteData[i]);
                persianText += persianDigits[digit];
            }
            
            // قرار دادن در فیلد
            const selectionStart = this.selectionStart;
            const selectionEnd = this.selectionEnd;
            
            // بروزرسانی نمایش با اعداد فارسی
            this.value = this.value.slice(0, selectionStart) + persianText + this.value.slice(selectionEnd);
            
            // بروزرسانی مقدار واقعی با اعداد انگلیسی
            actualValue = actualValue.slice(0, selectionStart) + pasteData + actualValue.slice(selectionEnd);
            
            // تنظیم موقعیت مکان‌نما
            this.selectionStart = this.selectionEnd = selectionStart + persianText.length;
        });
        
        // رویداد دکمه ماه قبل
        const prevMonthBtn = document.getElementById('prevMonthBtn');
        if (prevMonthBtn) {
            prevMonthBtn.addEventListener('click', showPreviousMonth);
        }
        
        // رویداد دکمه ماه بعد
        const nextMonthBtn = document.getElementById('nextMonthBtn');
        if (nextMonthBtn) {
            nextMonthBtn.addEventListener('click', showNextMonth);
        }
        
        // رویداد دکمه امروز
        const todayBtn = document.getElementById('todayBtn');
        if (todayBtn) {
            todayBtn.addEventListener('click', function() {
                const today = new Date();
                const todayJalali = jalaali.toJalaali(
                    today.getFullYear(),
                    today.getMonth() + 1,
                    today.getDate()
                );
                
                selectDate(todayJalali.jy, todayJalali.jm, todayJalali.jd);
            });
        }
        
        // رویداد دکمه پاک کردن
        const clearDateBtn = document.getElementById('clearDateBtn');
        if (clearDateBtn) {
            clearDateBtn.addEventListener('click', function() {
                // اگر فیلد فعال از نوع فیلتر است
                if (activeDateField && activeDateField.dataset.dateField) {
                    activeDateField.value = '';
                    activeDateField.removeAttribute('data-date');
                    activeDateField.removeAttribute('data-jalali-date');
                    document.getElementById('jalaliDatePickerModal').classList.add('hidden');
                    return;
                }
                
                // در غیر این صورت، عملیات پیش‌فرض برای دکمه پاک کردن تاریخ اقدام بعدی پرونده
                selectedJalaliDate = null;
                localStorage.removeItem(`nextActionDate_${getCurrentCaseId()}`);
                updateNextActionDateButton();
                document.getElementById('jalaliDatePickerModal').classList.add('hidden');
            });
        }
    }
}); 