<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فرم A4</title>
    <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazir-font@v30.1.0/dist/font-face.css" rel="stylesheet" type="text/css" />
    <style>
        @page {
            size: A4;
            margin: 0;
        }

        body {
            margin: 0;
            padding: 0;
            width: 210mm;
            min-height: 297mm;
            background-color: white;
            margin-left: auto;
            margin-right: auto;
            font-family: '<PERSON>azi<PERSON>', Tahoma, Arial, sans-serif;
        }

        .form-container {
            width: 100%;
            min-height: 297mm;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            padding-bottom: 15px;
        }

        .header {
            width: 90%;
            display: flex;
            justify-content: space-between;
            border: 1px solid #000;
            margin-top: 20px;
            box-sizing: border-box;
            overflow: hidden;
        }

        .header-section {
            height: 80px;
            border-right: 1px solid #000;
            display: flex;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;
        }

        .header-section:first-child {
            border-right: none;
        }

        .left-section {
            width: 25%;
            display: flex;
            flex-direction: column;
            padding: 0;
            box-sizing: border-box;
        }

        .left-subsection {
            display: flex;
            flex: 1;
            border-bottom: 1px solid #000;
            width: 100%;
            font-size: 12px;
            box-sizing: border-box;
            text-align: right;
        }

        .left-subsection:last-child {
            border-bottom: none;
        }

        .label-side {
            width: 30%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 10px;
            box-sizing: border-box;
            border-left: 1px solid #000;
            text-align: center;
        }

        .data-side {
            width: 70%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 10px;
            box-sizing: border-box;
            text-align: center;
        }

        .date-input {
            width: 100%;
            height: 100%;
            border: none;
            background-color: transparent;
            font-family: 'Vazir', Tahoma, Arial, sans-serif;
            text-align: center;
            font-size: 12px;
            direction: rtl;
            padding: 0;
            box-sizing: border-box;
            outline: none;
        }

        .middle-section {
            width: 50%;
            box-sizing: border-box;
        }

        .right-section {
            width: 25%;
            box-sizing: border-box;
        }

        .middle-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .middle-title {
            font-size: 22px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
        }

        .middle-subtitle {
            font-size: 11px;
            text-align: center;
            margin-top: 0;
        }

        .logo-img {
            max-height: 55px;
            max-width: 85%;
            object-fit: contain;
            margin-bottom: 0;
        }

        .logo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding-top: 5px;
            height: 100%;
        }

        .logo-text {
            font-size: 11px;
            margin-top: 0;
            font-weight: bold;
            line-height: 1;
        }

        .subtitle-text {
            position: absolute;
            top: 120px;
            right: 5%;
            font-size: 15px;
            font-weight: bold;
            text-align: right;
        }

        .subject-text {
            width: 90%;
            text-align: center;
            font-size: 15px;
            font-weight: bold;
            margin-top: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .subject-input {
            width: 120px;
            height: 25px;
            border: none;
            background-color: transparent;
            font-family: 'Vazir', Tahoma, Arial, sans-serif;
            text-align: center;
            font-size: 15px;
            margin: 0 -4px;
            padding: 0;
            direction: rtl;
            outline: none;
            position: relative;
            font-weight: bold;
        }

        .subject-input::placeholder {
            color: #aaa;
            opacity: 0.5;
            font-weight: normal;
        }

        .content-textarea {
            width: 100%;
            border: none;
            padding: 10px;
            font-family: 'Vazir', Tahoma, Arial, sans-serif;
            font-size: 16px;
            direction: rtl;
            resize: none;
            overflow: hidden;
            min-height: 25px;
            box-sizing: border-box;
            outline: none;
            position: relative;
            background-color: transparent;
            z-index: 2;
        }

        .content-textarea::placeholder {
            color: #aaa;
            opacity: 0.5;
            font-weight: normal;
        }

        .content-editable {
            width: 100%;
            border: none;
            padding: 5px;
            font-family: 'Vazir', Tahoma, Arial, sans-serif;
            font-size: 13px;
            direction: rtl;
            min-height: 20px;
            box-sizing: border-box;
            outline: none;
            position: relative;
            background-color: transparent;
            z-index: 2;
            white-space: pre-wrap;
            overflow-wrap: break-word;
        }

        .content-editable:empty:before {
            content: attr(data-placeholder);
            color: #aaa;
            opacity: 0.5;
            position: absolute;
            pointer-events: none;
        }

        .content-editable b {
            font-weight: bold;
        }

        .textarea-container {
            position: relative;
            width: 90%;
            margin-top: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 80px;
            color: rgba(200, 200, 200, 0.3);
            pointer-events: none;
            z-index: 1;
        }

        .evaluation-label {
            width: 90%;
            text-align: right;
            font-size: 13px;
            font-weight: bold;
            margin-top: 2px;
            margin-bottom: 5px;
        }

        [contenteditable="true"] {
            min-height: 16px;
            transition: background-color 0.2s;
            cursor: text;
            position: relative;
        }

        [contenteditable="true"]:hover {
            background-color: rgba(240, 240, 240, 0.5);
        }

        [contenteditable="true"]:focus {
            background-color: rgba(240, 240, 240, 0.8);
            outline: none;
        }

        [contenteditable="true"].empty:before {
            content: attr(data-placeholder);
            color: #aaa;
            font-style: italic;
            position: absolute;
            opacity: 0.6;
            pointer-events: none;
        }

        .officials-signature-section [contenteditable="true"] {
            min-height: 60px;
            position: relative;
            z-index: 2;
        }

        .officials-signature-section [contenteditable="true"].active {
            background-color: rgba(240, 240, 240, 0.8);
        }

        @media print {
            @page { /* Default for pages 2+ */
                size: A4;
                margin-top: 10mm; /* Further reduced top margin for subsequent pages */
                margin-right: 0;
                margin-bottom: 0;
                margin-left: 0;
            }

            @page :first { /* For page 1 */
                margin-top: 0; /* Keeps the first page's top margin effectively as is (content positioned by .header's margin) */
                /* Other margins (right, bottom, left) will inherit from the general @page rule, remaining 0 */
            }

            body {
                width: 210mm;
                min-height: 297mm;
                height: auto;
            }

            .form-container {
                min-height: 0;
                height: auto;
            }

            [contenteditable="true"]:hover,
            [contenteditable="true"]:focus {
                background-color: transparent;
            }

            .evaluation-table thead th {
                background-color: #e0e0e0 !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .officials-signature-section {
                page-break-inside: avoid;
                break-inside: avoid;
            }

            /* The old @page rule that was here is now replaced by the two above */
        }

        .evaluation-table {
            border-radius: 5px;
            border: none;
            border-collapse: collapse;
            border-spacing: 0;
        }

        .evaluation-table thead th:first-child {
            border-top-right-radius: 4px;
        }

        .evaluation-table thead th:last-child {
            border-top-left-radius: 4px;
        }

        .evaluation-table tbody tr:last-child td:first-child {
            border-bottom-right-radius: 4px;
        }

        .evaluation-table tbody tr:last-child td:last-child {
            border-bottom-left-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <div class="header">
            <div class="header-section left-section">
                <div class="left-subsection">
                    <div class="label-side">شماره</div>
                    <div class="data-side"></div>
                </div>
                <div class="left-subsection">
                    <div class="label-side">تاریخ</div>
                    <div class="data-side" id="current-date"></div>
                </div>
                <div class="left-subsection">
                    <div class="label-side">صفحه</div>
                    <div class="data-side">۱ از ۱</div>
                </div>
            </div>
            <div class="header-section middle-section">
                <div class="middle-content">
                    <div class="middle-title">فرم گزارش ارزیابی فنی و مالی</div>
                    <div class="middle-subtitle">خریدهای داخلی (جزئی و متوسط)</div>
                </div>
            </div>
            <div class="header-section right-section">
                <div class="logo-container">
                    <img src="Naft Logo.png" alt="لوگوی نفت" class="logo-img">
                    <div class="logo-text">مجتمع گاز پارس جنوبی</div>
                </div>
            </div>
        </div>

        <div class="subtitle-text">رئيس عملیات كالای پالايشگاه چهارم</div>

        <div class="subject-text">
            موضوع: ارزیابی فنی و مالی تقاضای شماره
            <input type="text" class="subject-input" placeholder="A1">
        </div>

        <div class="textarea-container">
            <div class="watermark">A2</div>
            <div class="content-editable" data-placeholder="متن خود را اینجا وارد کنید..."></div>
        </div>

        <div class="evaluation-label">الف: ارزیابی فنی</div>

        <table id="evaluation-table" class="evaluation-table" style="width: 90%; margin: 0 auto; direction: rtl;">
            <thead>
                <tr>
                    <th style="width: 10%; border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; background-color: #e0e0e0; font-weight: bold;">ردیف</th>
                    <th style="width: 45%; border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; background-color: #e0e0e0; font-weight: bold;">پیشنهادات مورد تائید</th>
                    <th style="width: 45%; border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; background-color: #e0e0e0; font-weight: bold;">پیشنهادات مردود</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; font-weight: normal;">۱</td>
                    <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle; font-weight: normal;" contenteditable="true" data-row="1" data-col="1"></td>
                    <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle; font-weight: normal;" contenteditable="true" data-row="1" data-col="2"></td>
                </tr>
            </tbody>
        </table>

        <div class="evaluation-label" style="text-align: right;">ب: ارزیابی مالی</div>

        <table id="financial-evaluation-table" class="evaluation-table" style="width: 90%; margin: 0 auto; direction: rtl;">
            <thead>
                <tr>
                    <th style="width: 10%; border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; background-color: #e0e0e0; font-weight: bold;">ردیف</th>
                    <th style="width: 45%; border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; background-color: #e0e0e0; font-weight: bold;">اولویت‌بندی پیشنهادات مالی</th>
                    <th style="width: 45%; border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; background-color: #e0e0e0; font-weight: bold;">مبلغ پیشنهادی (ریال)</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; font-weight: normal;">۱</td>
                    <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle; font-weight: normal;" contenteditable="true" data-row="1" data-col="1"></td>
                    <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle; font-weight: normal;" contenteditable="true" data-row="1" data-col="2"></td>
                </tr>
            </tbody>
        </table>

        <div class="evaluation-label" style="text-align: right; font-weight: bold; margin-top: 10px; margin-bottom: 0px; font-size: 13px;">اظهار نظر كارشناس/ ناظر خريد با توجه به ارزيابی به عمل آمده:</div>

        <div style="position: relative; width: 90%; margin: -3px auto 0 auto;">
            <div class="watermark" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 80px; color: rgba(200, 200, 200, 0.3); pointer-events: none; z-index: 1;">A3</div>
            <div id="expertComment" class="content-editable" style="width: 100%; margin: 0px 0; display: block; min-height: 40px; padding: 10px; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 13px; text-align: right; direction: rtl; border: none; background-color: transparent; box-sizing: border-box; position: relative; z-index: 2;" data-placeholder="نظر کارشناس خرید را اینجا وارد کنید..."></div>
        </div>

        <div class="officials-signature-section" style="page-break-inside: avoid; break-inside: avoid; width: 90%; margin: 0 auto;">
            <table class="evaluation-table" style="width: 100%; margin: 5px 0; direction: rtl;">
                <thead>
                    <tr>
                        <th style="width: 33.33%; border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; background-color: #e0e0e0; font-weight: bold;">کارشناس خرید</th>
                        <th style="width: 33.33%; border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; background-color: #e0e0e0; font-weight: bold;">رئیس کنترل و تدارکات کالا</th>
                        <th style="width: 33.33%; border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; background-color: #e0e0e0; font-weight: bold;">رئیس تدارکات و عملیات کالا</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="position: relative; border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 60px; line-height: 1;" contenteditable="true">
                            <div class="watermark" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 40px; color: rgba(200, 200, 200, 0.3); pointer-events: none; z-index: 1;">A4</div>
                        </td>
                        <td style="position: relative; border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 60px; line-height: 1;" contenteditable="true">
                            <div class="watermark" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 40px; color: rgba(200, 200, 200, 0.3); pointer-events: none; z-index: 1;">A5</div>
                        </td>
                        <td style="position: relative; border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 60px; line-height: 1;" contenteditable="true">
                            <div class="watermark" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 40px; color: rgba(200, 200, 200, 0.3); pointer-events: none; z-index: 1;">A6</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // تبدیل تاریخ میلادی به شمسی
        function gregorianToJalali(gy, gm, gd) {
            var g_d_m = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334];
            var jy, jm, jd, gy2, days;

            gy2 = (gm > 2) ? (gy + 1) : gy;
            days = 355666 + (365 * gy) + Math.floor((gy2 + 3) / 4) - Math.floor((gy2 + 99) / 100) + Math.floor((gy2 + 399) / 400) + gd + g_d_m[gm - 1];
            jy = -1595 + (33 * Math.floor(days / 12053));
            days %= 12053;
            jy += 4 * Math.floor(days / 1461);
            days %= 1461;

            if (days > 365) {
                jy += Math.floor((days - 1) / 365);
                days = (days - 1) % 365;
            }

            jm = (days < 186) ? 1 + Math.floor(days / 31) : 7 + Math.floor((days - 186) / 30);
            jd = 1 + ((days < 186) ? (days % 31) : ((days - 186) % 30));

            return [jy, jm, jd];
        }

        // تبدیل اعداد انگلیسی به فارسی
        function toPersianNumber(num) {
            const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
            return String(num).replace(/[0-9]/g, function(d) {
                return persianDigits[parseInt(d)];
            });
        }

        // نام ماه‌های شمسی
        const jalaliMonths = [
            'فروردین', 'اردیبهشت', 'خرداد', 'تیر', 'مرداد', 'شهریور',
            'مهر', 'آبان', 'آذر', 'دی', 'بهمن', 'اسفند'
        ];

        // تبدیل اعداد فارسی به انگلیسی
        function toEnglishNumber(num) {
            const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
            return String(num).replace(/[۰-۹]/g, function(d) {
                return persianDigits.indexOf(d);
            });
        }

        // گرفتن تاریخ امروز
        const today = new Date();
        const gregorianDate = [today.getFullYear(), today.getMonth() + 1, today.getDate()];
        const jalaliDate = gregorianToJalali(...gregorianDate);

        // قالب‌بندی تاریخ به صورت "روز ماه سال"
        const formattedDate = `${toPersianNumber(jalaliDate[2])} ${jalaliMonths[jalaliDate[1]-1]} ${toPersianNumber(jalaliDate[0])}`;

        // قرار دادن تاریخ در المان مربوطه
        document.getElementById('current-date').textContent = formattedDate;

        // Helper function to format numbers with thousand separators (for form.html)
        function formatNumberWithSeparators(numStr) {
            if (typeof numStr !== 'string') numStr = String(numStr);
            let parts = numStr.split('.');
            parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            return parts.join('.');
        }

        // Helper function to format price for the form (for form.html)
        function formatPriceForForm(priceStr) { // expects an English numeric string
            if (!priceStr || priceStr === '-' || priceStr.trim() === '') return '-';
            const formattedNum = formatNumberWithSeparators(priceStr);
            const persianNum = toPersianNumber(formattedNum);
            return persianNum;
        }

        // Helper function to convert all English numbers in text to Persian
        function convertTextNumbersToPersian(text) {
            if (!text) return text;
            return text.replace(/[0-9]/g, function(digit) {
                return toPersianNumber(digit);
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            const subjectInput = document.querySelector('.subject-input');
            subjectInput.addEventListener('input', function() {
                this.value = toPersianNumber(this.value);
            });

            // تنظیم ارتفاع خودکار برای textarea و مدیریت واترمارک
            const contentTextarea = document.querySelector('.content-editable');
            const watermark = document.querySelector('.watermark');

            contentTextarea.addEventListener('input', function() {
                // حذف واترمارک در صورت وجود متن
                if (this.textContent.trim() !== '') {
                    watermark.style.display = 'none';
                } else {
                    watermark.style.display = 'block';
                }
            });

            // تابع برای تنظیم ویژگی‌های سلول قابل ویرایش
            function setupEditableCell(cell) {
                // Initialize placeholder if cell is empty or already a placeholder
                if (cell.textContent.trim() === '' || cell.textContent === '-') {
                    cell.textContent = '-';
                    cell.classList.add('placeholder-active');
                } else {
                    cell.classList.remove('placeholder-active');
                }

                cell.addEventListener('focus', function() {
                    if (this.classList.contains('placeholder-active') && this.textContent === '-') {
                        this.textContent = '';
                        this.classList.remove('placeholder-active');
                    }
                });

                cell.addEventListener('blur', function() {
                    if (this.textContent.trim() === '') {
                        this.textContent = '-';
                        this.classList.add('placeholder-active');
                    } else {
                        // If content exists but is '-', it's a placeholder. Otherwise, remove class.
                        if (this.textContent !== '-') {
                            this.classList.remove('placeholder-active');
                        } else if (!this.classList.contains('placeholder-active')) { // Content is '-' but not marked as placeholder
                           this.classList.add('placeholder-active'); // Mark it
                        }
                    }
                });

                // اضافه کردن ردیف جدید با فشردن کلید اینتر
                cell.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        // تشخیص جدول مربوطه و اضافه کردن ردیف به همان جدول
                        const closestTable = this.closest('table');
                        if (closestTable.id === 'financial-evaluation-table') {
                            addNewFinancialRow();
                        } else {
                            addNewRow();
                        }

                        // بعد از اضافه کردن ردیف، بررسی مجموع ردیف‌ها و تنظیم اندازه
                        adjustRowSizes();
                    }
                });
            }

            // تابع برای بررسی و تنظیم اندازه ردیف‌ها براساس تعداد کل
            function adjustRowSizes() {
                const technicalRows = document.getElementById('evaluation-table').querySelector('tbody').querySelectorAll('tr');
                const financialRows = document.getElementById('financial-evaluation-table').querySelector('tbody').querySelectorAll('tr');

                const totalRows = technicalRows.length + financialRows.length;
                const maxRows = 22;

                // سلول‌های هر دو جدول
                const allCells = document.querySelectorAll('#evaluation-table tbody td, #financial-evaluation-table tbody td');

                if (totalRows > maxRows) {
                    // محاسبه ضریب کاهش برای حفظ فضای کلی
                    const scaleFactor = maxRows / totalRows;

                    // محاسبه مقادیر جدید برای ارتفاع و اندازه فونت
                    const newHeight = Math.max(Math.floor(9 * scaleFactor), 3); // حداقل ارتفاع 3px (کاهش از 4px)
                    const newFontSize = Math.max(Math.floor(11 * scaleFactor), 8); // حداقل اندازه فونت 8px، بر اساس سایز پیش‌فرض 11px
                    const newPadding = Math.max(Math.floor(5 * scaleFactor), 1); // حداقل پدینگ 1px (کاهش از 2px)

                    // اعمال استایل‌های جدید به همه سلول‌ها
                    allCells.forEach(cell => {
                        cell.style.height = newHeight + 'px';
                        cell.style.fontSize = newFontSize + 'px';
                        cell.style.padding = newPadding + 'px';
                        cell.style.fontWeight = 'bold';
                    });
                } else {
                    // برگرداندن به حالت پیش‌فرض اگر تعداد ردیف‌ها کمتر از حداکثر است
                    allCells.forEach(cell => {
                        cell.style.height = '9px';
                        cell.style.fontSize = '11px';
                        cell.style.padding = '5px';
                        cell.style.fontWeight = 'normal';
                    });
                }
            }

            // تابع برای اضافه کردن ردیف جدید به جدول
            function addNewRow() {
                const table = document.getElementById('evaluation-table');
                const tbody = table.querySelector('tbody');
                const rows = tbody.querySelectorAll('tr');
                const lastRow = rows[rows.length - 1];
                const newRow = document.createElement('tr');

                // شماره ردیف جدید (به فارسی)
                const rowNumber = rows.length + 1;
                const persianRowNumber = toPersianNumber(rowNumber);

                // ساخت سلول‌های ردیف جدید
                newRow.innerHTML = `
                    <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1;">${persianRowNumber}</td>
                    <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle;" contenteditable="true" data-row="${rowNumber}" data-col="1"></td>
                    <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle;" contenteditable="true" data-row="${rowNumber}" data-col="2"></td>
                `;

                // اضافه کردن ردیف به جدول
                tbody.appendChild(newRow);

                // اضافه کردن ویژگی‌های contenteditable به سلول‌های جدید
                const newCells = newRow.querySelectorAll('[contenteditable="true"]');
                newCells.forEach(cell => {
                    setupEditableCell(cell);
                });

                // تنظیم اندازه‌ها پس از اضافه کردن ردیف
                adjustRowSizes();

                // فوکوس روی اولین سلول قابل ویرایش در ردیف جدید
                newCells[0].focus();

                return newCells[0];
            }

            // تابع برای اضافه کردن ردیف جدید به جدول ارزیابی مالی
            function addNewFinancialRow() {
                const table = document.getElementById('financial-evaluation-table');
                const tbody = table.querySelector('tbody');
                const rows = tbody.querySelectorAll('tr');
                const lastRow = rows[rows.length - 1];
                const newRow = document.createElement('tr');

                // شماره ردیف جدید (به فارسی)
                const rowNumber = rows.length + 1;
                const persianRowNumber = toPersianNumber(rowNumber);

                // ساخت سلول‌های ردیف جدید
                newRow.innerHTML = `
                    <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1;">${persianRowNumber}</td>
                    <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle;" contenteditable="true" data-row="${rowNumber}" data-col="1"></td>
                    <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle;" contenteditable="true" data-row="${rowNumber}" data-col="2"></td>
                `;

                // اضافه کردن ردیف به جدول
                tbody.appendChild(newRow);

                // اضافه کردن ویژگی‌های contenteditable به سلول‌های جدید
                const newCells = newRow.querySelectorAll('[contenteditable="true"]');
                newCells.forEach(cell => {
                    setupEditableCell(cell);
                });

                // تنظیم اندازه‌ها پس از اضافه کردن ردیف
                adjustRowSizes();

                // فوکوس روی اولین سلول قابل ویرایش در ردیف جدید
                newCells[0].focus();

                return newCells[0];
            }

            // مدیریت سلول‌های قابل ویرایش جدول
            const editableCells = document.querySelectorAll('[contenteditable="true"]');

            // تنظیم سلول‌های قابل ویرایش موجود
            editableCells.forEach(cell => {
                setupEditableCell(cell);
            });

            // فراخوانی اولیه تابع تنظیم اندازه‌ها
            window.addEventListener('load', adjustRowSizes);

            // مدیریت واترمارک‌های جدول مسئولین
            const officialCells = document.querySelectorAll('.officials-signature-section [contenteditable="true"]');
            officialCells.forEach(cell => {
                const watermark = cell.querySelector('.watermark');

                // اضافه کردن رویداد input برای مدیریت واترمارک
                cell.addEventListener('input', function() {
                    if (this.textContent.trim() !== '') {
                        watermark.style.display = 'none';
                    } else {
                        watermark.style.display = 'block';
                    }
                });

                // رویداد focus برای افزودن کلاس active
                cell.addEventListener('focus', function() {
                    this.classList.add('active');
                });

                // رویداد blur برای حذف کلاس active
                cell.addEventListener('blur', function() {
                    this.classList.remove('active');
                });
            });

            // Auto-resize for textarea (direct implementation)
            var textarea = document.getElementById('expertComment');

            // Function to resize textarea
            function resizeTextarea() {
                if (!textarea) return;
                textarea.style.height = 'auto';
                textarea.style.height = (textarea.scrollHeight) + 'px';
            }

            if (textarea) {
                // Initial setup
                textarea.style.height = 'auto';
                textarea.style.height = textarea.scrollHeight + 'px';

                // Add multiple event listeners
                textarea.addEventListener('input', resizeTextarea);
                textarea.addEventListener('keyup', resizeTextarea);
                textarea.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        setTimeout(resizeTextarea, 10);
                    }
                });
                textarea.addEventListener('focus', resizeTextarea);
                textarea.addEventListener('change', resizeTextarea);

                // Also trigger on window load
                window.addEventListener('load', resizeTextarea);

                // Immediate resize
                resizeTextarea();
            }

            // Load data from localStorage on page load
            const purchaseReqNumberA1 = localStorage.getItem('formData_A1');
            if (purchaseReqNumberA1) {
                // Convert to Persian numbers before displaying
                const persianA1 = toPersianNumber(purchaseReqNumberA1);

                // Set the value in the subject input
                const subjectInput = document.querySelector('.subject-input');
                if (subjectInput) {
                    subjectInput.value = persianA1;
                }

                // Also set the value in the header number field
                const headerNumberField = document.querySelector('.header .left-section .left-subsection:first-child .data-side');
                if (headerNumberField) {
                    headerNumberField.textContent = persianA1;
                }
            }

            // Load A2 template text from localStorage and populate content textarea
            const a2TemplateText = localStorage.getItem('formData_A2');
            if (a2TemplateText) {
                const contentTextarea = document.querySelector('.content-editable');
                if (contentTextarea) {
                    contentTextarea.innerHTML = a2TemplateText;

                    // Hide watermark since we have content
                    const watermark = document.querySelector('.watermark');
                    if (watermark) {
                        watermark.style.display = 'none';
                    }
                }
            }

            // Load approved suppliers and populate tables
            const approvedSuppliersDataJSON = localStorage.getItem('formData_approvedSuppliersData');
            const unapprovedSuppliersDataJSON = localStorage.getItem('formData_unapprovedSuppliersData'); // Changed key
            const transactionLimitStr = localStorage.getItem('formData_transactionLimit');

            const transactionLimitNum = transactionLimitStr && transactionLimitStr !== '-' ? parseFloat(transactionLimitStr) : null;

            if (approvedSuppliersDataJSON || unapprovedSuppliersDataJSON) { // Check if any supplier data exists
                const approvedSuppliersData = approvedSuppliersDataJSON ? JSON.parse(approvedSuppliersDataJSON) : [];
                const unapprovedSuppliersData = unapprovedSuppliersDataJSON ? JSON.parse(unapprovedSuppliersDataJSON) : [];

                const technicalTableBody = document.getElementById('evaluation-table').querySelector('tbody');
                technicalTableBody.innerHTML = '';

                const numApproved = approvedSuppliersData.length;
                const numUnapproved = unapprovedSuppliersData.length;
                const maxRowsTechnical = Math.max(numApproved, numUnapproved);

                if (maxRowsTechnical > 0) {
                    for (let i = 0; i < maxRowsTechnical; i++) {
                        const rowNumberDisplay = toPersianNumber(i + 1);
                        const approvedSupplierNameText = (i < numApproved) ? approvedSuppliersData[i].name : '';

                        let unapprovedSupplierDisplayText = '';
                        if (i < numUnapproved) {
                            const unapprovedSupplier = unapprovedSuppliersData[i];
                            unapprovedSupplierDisplayText = unapprovedSupplier.name;
                            if (unapprovedSupplier.price && unapprovedSupplier.price !== '-' && transactionLimitNum !== null) {
                                const supplierPriceNum = parseFloat(unapprovedSupplier.price);
                                if (!isNaN(supplierPriceNum) && supplierPriceNum > transactionLimitNum) {
                                    unapprovedSupplierDisplayText += ' (قیمت عمده)';
                                }
                            }
                        }

                        const techRow = document.createElement('tr');
                        techRow.innerHTML = `
                            <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; font-weight: normal;">${rowNumberDisplay}</td>
                            <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle; font-weight: normal;" contenteditable="true" data-row="${i + 1}" data-col="1">${approvedSupplierNameText}</td>
                            <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle; font-weight: normal;" contenteditable="true" data-row="${i + 1}" data-col="2">${unapprovedSupplierDisplayText}</td>
                        `;
                        technicalTableBody.appendChild(techRow);
                        techRow.querySelectorAll('[contenteditable="true"]').forEach(cell => setupEditableCell(cell));
                    }
                } else if (technicalTableBody.children.length === 0) {
                    const techRow = document.createElement('tr');
                     techRow.innerHTML = `
                        <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; font-weight: normal;">${toPersianNumber(1)}</td>
                        <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle; font-weight: normal;" contenteditable="true" data-row="1" data-col="1"></td>
                        <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle; font-weight: normal;" contenteditable="true" data-row="1" data-col="2"></td>
                    `;
                    technicalTableBody.appendChild(techRow);
                    techRow.querySelectorAll('[contenteditable="true"]').forEach(cell => setupEditableCell(cell));
                }

                // Populate Financial Table only with Approved Suppliers Data (name and price)
                if (approvedSuppliersData && approvedSuppliersData.length > 0) {
                    const financialTableBody = document.getElementById('financial-evaluation-table').querySelector('tbody');
                    financialTableBody.innerHTML = '';

                    approvedSuppliersData.forEach((supplierData, index) => {
                        const rowNumber = index + 1;
                        const persianRowNumber = toPersianNumber(rowNumber);
                        const formattedPrice = formatPriceForForm(supplierData.price);

                        const finRow = document.createElement('tr');
                        finRow.innerHTML = `
                            <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; font-weight: normal;">${persianRowNumber}</td>
                            <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle; font-weight: normal;" contenteditable="true" data-row="${rowNumber}" data-col="1">${supplierData.name}</td>
                            <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle; font-weight: normal;" contenteditable="true" data-row="${rowNumber}" data-col="2">${formattedPrice}</td>
                        `;
                        financialTableBody.appendChild(finRow);
                        finRow.querySelectorAll('[contenteditable="true"]').forEach(cell => setupEditableCell(cell));
                    });
                } else { // If no approved suppliers, add a default empty row to financial table
                    const financialTableBody = document.getElementById('financial-evaluation-table').querySelector('tbody');
                    financialTableBody.innerHTML = ''; // Clear it first
                    const finRow = document.createElement('tr');
                    finRow.innerHTML = `
                        <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; font-weight: normal;">${toPersianNumber(1)}</td>
                        <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle; font-weight: normal;" contenteditable="true" data-row="1" data-col="1"></td>
                        <td style="border: 0.1px solid #000; padding: 5px; text-align: center; font-family: 'Vazir', Tahoma, Arial, sans-serif; font-size: 11px; height: 9px; line-height: 1; vertical-align: middle; font-weight: normal;" contenteditable="true" data-row="1" data-col="2"></td>
                    `;
                    financialTableBody.appendChild(finRow);
                    finRow.querySelectorAll('[contenteditable="true"]').forEach(cell => setupEditableCell(cell));
                }

                adjustRowSizes();
            }

            // Setup event listener for expert comment area
            const expertComment = document.getElementById('expertComment');
            const expertWatermark = expertComment.previousElementSibling;

            expertComment.addEventListener('input', function() {
                // Convert English numbers to Persian in real-time
                const currentText = this.innerHTML;
                const persianText = convertTextNumbersToPersian(currentText);
                if (currentText !== persianText) {
                    // Save cursor position
                    const selection = window.getSelection();
                    const range = selection.getRangeAt(0);
                    const offset = range.startOffset;

                    // Update text
                    this.innerHTML = persianText;

                    // Restore cursor position
                    try {
                        const newRange = document.createRange();
                        newRange.setStart(this.firstChild || this, Math.min(offset, (this.textContent || '').length));
                        newRange.collapse(true);
                        selection.removeAllRanges();
                        selection.addRange(newRange);
                    } catch (e) {
                        // If cursor positioning fails, just continue
                    }
                }

                if (this.textContent.trim() !== '') {
                    expertWatermark.style.display = 'none';
                } else {
                    expertWatermark.style.display = 'block';
                }
            });

            // Load expert recommendation text from localStorage for A3
            const expertRecommendationText = localStorage.getItem('formData_A3');
            if (expertRecommendationText && expertComment) {
                // Check if this is the "خرید از برنده" recommendation
                if (expertRecommendationText.includes('خريد از شركت x با مبلغ y ریال')) {
                    // This is a deferred function that will execute after approved suppliers data is loaded
                    // and after the financial evaluation table is populated
                    const updateWinnerRecommendation = function() {
                        // Get the company name and price from the first row of the financial evaluation table
                        const financialTable = document.getElementById('financial-evaluation-table');
                        if (financialTable) {
                            const firstRow = financialTable.querySelector('tbody tr:first-child');
                            if (firstRow) {
                                const nameCell = firstRow.querySelector('td:nth-child(2)');
                                const priceCell = firstRow.querySelector('td:nth-child(3)');

                                if (nameCell && priceCell) {
                                    const companyName = nameCell.textContent.trim();
                                    const companyPrice = priceCell.textContent.trim();

                                    if (companyName && companyName !== '-' && companyPrice && companyPrice !== '-') {
                                        // Replace placeholders with company name and price (in bold)
                                        let updatedText = expertRecommendationText
                                            .replace('شركت x', 'شركت <b>' + companyName + '</b>')
                                            .replace('مبلغ y ریال', 'مبلغ <b>' + companyPrice + '</b> ریال');

                                        // Convert any remaining English numbers to Persian
                                        updatedText = convertTextNumbersToPersian(updatedText);

                                        expertComment.innerHTML = updatedText;
                                        if (expertWatermark) {
                                            expertWatermark.style.display = 'none';
                                        }
                                        // Resize after setting content
                                        resizeTextarea();
                                        return; // Exit the function as we've handled the text
                                    }
                                }
                            }
                        }

                        // If we couldn't find valid data, convert numbers to Persian and set the original text
                        const persianText = convertTextNumbersToPersian(expertRecommendationText);
                        expertComment.innerHTML = persianText;
                        // Resize after setting content
                        resizeTextarea();
                    };

                    // If the table is already populated, run immediately
                    // Otherwise, set a timeout to run after the table is likely populated
                    setTimeout(updateWinnerRecommendation, 500);
                } else {
                    // For other recommendations, convert numbers to Persian and set the text
                    const persianText = convertTextNumbersToPersian(expertRecommendationText);
                    expertComment.innerHTML = persianText;
                    // Resize after setting content
                    resizeTextarea();
                }

                if (expertWatermark && expertRecommendationText.trim() !== '') {
                    expertWatermark.style.display = 'none';
                }
            }

            // Load officials data from localStorage
            const officialsCells = document.querySelectorAll('.officials-signature-section [contenteditable="true"]');

            // Map the cells to their respective data keys
            const officialsDataMap = {
                0: 'formData_A4', // Purchasing Expert
                1: 'formData_A5', // Purchasing Manager
                2: 'formData_A6'  // Goods Unit Manager
            };

            // Populate officials cells
            officialsCells.forEach((cell, index) => {
                const dataKey = officialsDataMap[index];
                if (dataKey) {
                    const officialName = localStorage.getItem(dataKey);
                    if (officialName) {
                        cell.textContent = officialName;
                        // Hide watermark since we have content
                        const watermark = cell.querySelector('.watermark');
                        if (watermark) {
                            watermark.style.display = 'none';
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>