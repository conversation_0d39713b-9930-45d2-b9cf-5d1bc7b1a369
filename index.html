<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استخراج اطلاعات تامین کنندگان</title>
    <!-- Add Vazir font -->
    <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazir-font@v30.1.0/dist/font-face.css" rel="stylesheet" type="text/css" />
    <style>
        :root {
            --primary-color: #e0e5ec;
            --shadow-color-dark: #a3b1c6;
            --shadow-color-light: #ffffff;
            --text-color: #4a4a4a;
            --accent-color: #6d5dfc;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Vazir', 'Tahoma', sans-serif;
        }

        body {
            background-color: var(--primary-color);
            color: var(--text-color);
            padding: 2rem;
        }

        /* Add new style to prevent scrolling when modal is open */
        body.modal-open {
            overflow: hidden;
        }

        .container {
            max-width: 1320px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--text-color);
            text-shadow: 1px 1px 1px var(--shadow-color-light);
        }

        .neomorphic {
            border-radius: 15px;
            background: var(--primary-color);
            box-shadow: 8px 8px 16px var(--shadow-color-dark),
                        -8px -8px 16px var(--shadow-color-light);
            padding: 20px;
            margin-bottom: 2rem;
        }

        .neomorphic-inset {
            border-radius: 15px;
            background: var(--primary-color);
            box-shadow: inset 8px 8px 16px var(--shadow-color-dark),
                        inset -8px -8px 16px var(--shadow-color-light);
            padding: 20px;
        }

        .text-area {
            width: 100%;
            min-height: 50px;
            height: 50px;
            border: none;
            background: var(--primary-color);
            color: var(--text-color);
            font-size: 16px;
            resize: vertical;
            padding: 0 15px;
            outline: none;
            margin-bottom: 10px;
            border-radius: 15px;
            box-shadow: inset 8px 8px 16px var(--shadow-color-dark),
                        inset -8px -8px 16px var(--shadow-color-light);
            line-height: 50px; /* همان ارتفاع textarea */
        }

        /* استایل برای متن placeholder */
        .text-area::placeholder {
            line-height: 50px; /* همان ارتفاع textarea */
        }

        .full-width-btn {
            display: block;
            width: 100%;
            margin-bottom: 10px;
            text-align: center;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            border-radius: 40px;
            border: none;
            background: var(--primary-color);
            color: var(--text-color);
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 5px 5px 10px var(--shadow-color-dark),
                        -5px -5px 10px var(--shadow-color-light);
            transition: all 0.2s ease;
            margin-bottom: 20px;
        }

        .btn.normal-weight {
            font-weight: normal;
        }

        /* Ensure button text stays on one line */
        .btn {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .btn:hover {
            box-shadow: 7px 7px 14px var(--shadow-color-dark),
                        -7px -7px 14px var(--shadow-color-light);
        }

        .btn:active {
            box-shadow: inset 5px 5px 10px var(--shadow-color-dark),
                        inset -5px -5px 10px var(--shadow-color-light);
        }

        .info-box {
            margin-bottom: 20px;
        }

        .info-item {
            display: flex;
            margin-bottom: 10px;
        }

        .info-label {
            min-width: 200px;
            font-weight: bold;
        }

        .info-value {
            flex-grow: 1;
            padding: 8px 15px;
            border-radius: 10px;
            background: var(--primary-color);
            box-shadow: inset 3px 3px 6px var(--shadow-color-dark),
                        inset -3px -3px 6px var(--shadow-color-light);
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0 10px;
            margin-bottom: 25px;
        }

        /* Remove the old th styling since we're using a new approach */

        /* Column width adjustment */
        th:nth-child(1), td:nth-child(1) { width: 10%; }
        th:nth-child(2), td:nth-child(2) { width: 40%; }
        th:nth-child(3), td:nth-child(3) { width: 35%; }
        th:nth-child(4), td:nth-child(4) { width: 15%; }

        tr {
            transition: all 0.2s ease;
        }

        tbody tr {
            box-shadow: 3px 3px 6px var(--shadow-color-dark),
                       -3px -3px 6px var(--shadow-color-light);
            border-radius: 10px;
            cursor: pointer;
        }

        tbody tr:hover {
            box-shadow: 3px 3px 6px var(--shadow-color-dark),
                       -3px -3px 6px var(--shadow-color-light);
            transform: none;
        }

        td {
            padding: 7px 15px;
            background-color: var(--primary-color);
            text-align: center;
            border: none;
        }

        tbody tr td:first-child {
            border-radius: 0 10px 10px 0;
        }

        tbody tr td:last-child {
            border-radius: 10px 0 0 10px;
        }

        /* Fix for row appearance */
        tbody tr td {
            background-color: transparent;
        }

        tbody tr {
            background-color: var(--primary-color);
        }

        .editable-input {
            width: 100%;
            padding: 8px;
            background-color: var(--primary-color);
            border: none;
            border-radius: 10px;
            color: var(--text-color);
            font-size: 16px;
            box-shadow: inset 3px 3px 5px var(--shadow-color-dark),
                        inset -3px -3px 5px var(--shadow-color-light);
            transition: all 0.3s ease;
            text-align: center;
        }

        .editable-input:focus {
            outline: none;
            box-shadow: inset 4px 4px 6px var(--shadow-color-dark),
                        inset -4px -4px 6px var(--shadow-color-light);
        }

        .small-input {
            width: auto;
            min-width: 150px;
            max-width: 200px;
            text-align: center;
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            overflow-y: auto;
            direction: rtl;
        }

        .modal-content {
            position: relative;
            margin: 5% auto;
            width: 120%;
            max-width: 1320px;
            border-radius: 15px;
            background: var(--primary-color);
            padding: 25px;
        }

        .close-btn {
            position: absolute;
            top: 15px;
            left: 15px;
            font-size: 16px;
            cursor: pointer;
            min-width: 120px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
            color: white;
            box-shadow: 3px 3px 6px var(--shadow-color-dark),
                       -3px -3px 6px var(--shadow-color-light);
            transition: opacity 0.2s ease;
        }

        .close-btn:hover {
            box-shadow: 5px 5px 10px var(--shadow-color-dark),
                       -5px -5px 10px var(--shadow-color-light);
            opacity: 0.9;
        }

        .close-btn:active {
            box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .save-btn {
            position: absolute;
            top: 15px;
            left: 145px;
            font-size: 16px;
            cursor: pointer;
            min-width: 120px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
            color: white;
            border: none;
            box-shadow: 3px 3px 6px var(--shadow-color-dark),
                       -3px -3px 6px var(--shadow-color-light);
            transition: opacity 0.2s ease;
        }

        .save-btn:hover {
            box-shadow: 5px 5px 10px var(--shadow-color-dark),
                       -5px -5px 10px var(--shadow-color-light);
            opacity: 0.9;
        }

        .save-btn:active {
            box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .setting-section {
            margin-bottom: 25px;
            padding: 15px;
            border-radius: 10px;
            background: var(--primary-color);
            box-shadow: inset 3px 3px 6px var(--shadow-color-dark),
                        inset -3px -3px 6px var(--shadow-color-light);
        }

        .setting-section h3 {
            margin-bottom: 15px;
            color: var(--accent-color);
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .settings-btn {
            position: fixed;
            bottom: 30px;
            left: 30px;
            width: 60px;
            height: 60px;
            border-radius: 12px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 5px 5px 10px var(--shadow-color-dark),
                      -5px -5px 10px var(--shadow-color-light);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 100;
        }

        .settings-btn:hover {
            box-shadow: 7px 7px 14px var(--shadow-color-dark),
                      -7px -7px 14px var(--shadow-color-light);
        }

        .settings-btn:active {
            box-shadow: inset 3px 3px 6px var(--shadow-color-dark),
                        inset -3px -3px 6px var(--shadow-color-light);
        }

        .settings-icon {
            width: 30px;
            height: 30px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236d5dfc"><path d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z"/></svg>');
            background-repeat: no-repeat;
            background-position: center;
        }

        .recommendation-item {
            display: flex;
            align-items: flex-start;
            padding: 10px 15px;
            transition: all 0.2s ease;
            background: var(--primary-color);
            direction: rtl;
            margin-bottom: 10px;
            border-radius: 10px;
        }

        .recommendation-item:hover {
            background: rgba(66, 133, 244, 0.05);
        }

        .recommendation-title {
            font-weight: bold;
            color: var(--accent-color);
            font-size: 14px;
            min-width: 220px;
            flex: 0.4;
            text-align: right;
            padding-left: 15px;
            align-self: center; /* Changed from flex-start to center */
            padding-top: 0; /* Remove top padding since we're centering vertically */
        }

        .recommendation-text {
            font-size: 13px;
            flex: 1;
            text-align: right;
            padding-left: 10px;
            padding-right: 10px;
            margin-left: 10px;
            overflow-wrap: break-word;
            word-wrap: break-word;
            overflow: visible;
            align-self: center; /* Changed from flex-start to center */
            padding-top: 0; /* Remove top padding since we're centering vertically */
            max-width: calc(100% - 400px);
        }

        .recommendation-editable {
            background: transparent;
            border: none;
            outline: none;
            font-family: inherit;
            font-size: inherit;
            color: inherit;
            width: 100%;
        }

        .recommendation-editable:focus {
            background-color: rgba(255, 255, 255, 0.2);
            border-bottom: 1px dashed var(--accent-color);
        }

        .recommendation-actions {
            display: flex;
            gap: 5px;
            justify-content: flex-end;
            min-width: 140px;
            margin-right: 10px;
            align-self: center; /* Changed from flex-start to center */
            padding-top: 0; /* Remove top padding since we're centering vertically */
        }

        .recommendation-container {
            height: 400px;
            overflow-y: auto;
            margin-bottom: 15px;
            border-radius: 10px;
            box-shadow: inset 3px 3px 6px var(--shadow-color-dark),
                        inset -3px -3px 6px var(--shadow-color-light);
            padding: 10px 0;
            direction: rtl;
        }

        .edit-btn {
            width: auto;
            min-width: 60px;
            height: 28px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 8px;
            font-size: 12px;
            border: none;
            background: var(--primary-color);
            color: #FF8800;
            cursor: pointer;
            box-shadow: 3px 3px 6px var(--shadow-color-dark),
                      -3px -3px 6px var(--shadow-color-light);
            margin-left: 5px;
            transition: none;
        }

        .edit-btn:hover {
            box-shadow: 4px 4px 8px var(--shadow-color-dark),
                      -4px -4px 8px var(--shadow-color-light);
            transform: none;
        }

        .edit-btn:active {
            box-shadow: inset 2px 2px 4px var(--shadow-color-dark),
                      inset -2px -2px 4px var(--shadow-color-light);
        }

        .delete-btn {
            width: auto;
            min-width: 60px;
            height: 28px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 8px;
            font-size: 12px;
            font-weight: normal;
            border: none;
            background: var(--primary-color);
            color: #FF3B30;
            cursor: pointer;
            box-shadow: 3px 3px 6px var(--shadow-color-dark),
                      -3px -3px 6px var(--shadow-color-light);
            transition: none;
        }

        .delete-btn:hover {
            box-shadow: 4px 4px 8px var(--shadow-color-dark),
                      -4px -4px 8px var(--shadow-color-light);
            transform: none;
        }

        .delete-btn:active {
            box-shadow: inset 2px 2px 4px var(--shadow-color-dark),
                      inset -2px -2px 4px var(--shadow-color-light);
        }

        .delete-btn {
            background: var(--primary-color);
            color: #FF3B30;
            font-size: 12px;
            cursor: pointer;
            padding: 0 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: none;
            transform: none;
        }

        .add-recommendation-btn {
            margin-top: 10px;
            background: var(--accent-color);
            color: white;
        }

        textarea.editable-input {
            min-height: 100px;
            resize: vertical;
        }

        /* Add new styles for the top controls row */
        .controls-row {
            display: flex;
            flex-wrap: nowrap;
            gap: 5px;
            margin: 0 0 25px 0;
            align-items: center;
            width: 100%;
        }

        .input-control {
            display: flex;
            flex: 1;
            align-items: stretch;
            border-radius: 15px;
            background: var(--primary-color);
            box-shadow: 5px 5px 10px var(--shadow-color-dark),
                       -5px -5px 10px var(--shadow-color-light);
            padding: 0;
            overflow: hidden;
            margin: 0;
            border-radius: 15px;
            height: 44px; /* Fixed height for consistent appearance */
        }

        .input-control input {
            border: none;
            padding: 0;
            margin: 0;
            outline: none;
            text-align: center;
            font-size: 18px; /* Increased from 16px */
            flex: 1;
            color: white;
            font-weight: normal; /* Remove bold formatting */
            background: transparent;
            border-radius: 15px;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Placeholder styles for better centering */
        .input-control input::placeholder {
            color: rgba(255, 255, 255, 0.85);
            opacity: 1; /* Firefox */
            font-weight: normal; /* Remove bold formatting */
            text-align: center;
        }

        /* For older browsers */
        .input-control input::-webkit-input-placeholder {
            color: rgba(255, 255, 255, 0.85);
            opacity: 1;
            font-weight: normal; /* Remove bold formatting */
            text-align: center;
        }

        .input-control input:-ms-input-placeholder {
            color: rgba(255, 255, 255, 0.85);
            opacity: 1;
            font-weight: normal; /* Remove bold formatting */
            text-align: center;
        }

        /* Need number - red gradient */
        .input-control.need-label {
            background: linear-gradient(135deg, rgba(229, 57, 53, 0.85) 0%, rgba(183, 28, 28, 0.9) 100%);
        }

        /* Purchase request - orange gradient */
        .input-control.purchase-label {
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.9) 0%, rgba(230, 81, 0, 0.85) 100%);
        }

        /* Count - green gradient (moved from inquiry) */
        .input-control.count-label {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.9) 0%, rgba(0, 121, 107, 0.85) 100%);
        }



        /* Exact iOS Toggle Switch Design with Neomorphism */
        .toggle-btn {
            position: relative;
            width: 67px;
            height: 31px;
            border-radius: 15.5px;
            background: var(--primary-color);
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            margin: 0 auto;
            display: block;
            border: none;
            outline: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            box-shadow: inset 4px 4px 8px var(--shadow-color-dark),
                        inset -4px -4px 8px var(--shadow-color-light);
        }

        /* Toggle knob (circle) with neomorphism */
        .toggle-btn::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 27px;
            height: 27px;
            border-radius: 50%;
            background: var(--primary-color);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 3px 3px 6px var(--shadow-color-dark),
                        -3px -3px 6px var(--shadow-color-light);
        }

        /* Active state - clear green background with neomorphism */
        .toggle-btn.active {
            background: #34C759;
            box-shadow: inset 4px 4px 8px rgba(52, 199, 89, 0.4),
                        inset -4px -4px 8px rgba(52, 199, 89, 0.2);
        }

        /* Active state - knob moves to right */
        .toggle-btn.active::after {
            transform: translateX(36px);
            box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.2),
                        -3px -3px 6px rgba(0, 0, 0, 0.1);
        }

        /* Text labels positioned outside the toggle */
        .toggle-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            font-weight: 400;
            color: var(--text-color);
            white-space: nowrap;
            pointer-events: none;
        }

        /* Simple wrapper for toggle without labels */
        .toggle-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
        }



        /* Press effect with deeper inset */
        .toggle-btn:active {
            box-shadow: inset 6px 6px 12px var(--shadow-color-dark),
                        inset -6px -6px 12px var(--shadow-color-light);
        }

        .toggle-btn:active::after {
            width: 29px;
            box-shadow: inset 2px 2px 4px var(--shadow-color-dark),
                        inset -2px -2px 4px var(--shadow-color-light);
        }

        .toggle-btn.active:active::after {
            width: 29px;
            box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3),
                        inset -2px -2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-btn.active:active {
            background: #28A745;
            box-shadow: inset 6px 6px 12px rgba(52, 199, 89, 0.7),
                        inset -6px -6px 12px rgba(52, 199, 89, 0.4);
        }

        /* Add styles for the unified header button and its cells */
        .header-row {
            margin-bottom: 10px;
            width: 100%;
        }

        .header-unified {
            display: flex;
            width: 100%;
            height: 44px;
            align-items: center;
            background: linear-gradient(135deg, rgba(120, 125, 136, 0.95) 0%, rgba(90, 95, 110, 0.95) 100%);
            color: white;
            border-radius: 10px;
            box-shadow: 5px 5px 10px var(--shadow-color-dark),
                      -5px -5px 10px var(--shadow-color-light);
            padding: 0;
            font-size: 16px;
        }

        .header-cell {
            text-align: center;
            padding: 0 5px;
        }

        .header-cell:nth-child(1) { width: 10%; }
        .header-cell:nth-child(2) { width: 40%; }
        .header-cell:nth-child(3) { width: 35%; }
        .header-cell:nth-child(4) { width: 15%; }

        /* Add a style to hide the table by default */
        .hidden {
            display: none;
        }

        /* Add a style for the settings form row */
        .settings-row {
            display: flex;
            flex-wrap: nowrap;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
            width: 100%;
        }

        .settings-input {
            flex: 1;
            padding: 12px 15px;
            border-radius: 10px;
            background-color: var(--primary-color);
            box-shadow: inset 3px 3px 5px var(--shadow-color-dark),
                        inset -3px -3px 5px var(--shadow-color-light);
            color: var(--text-color);
            border: none;
            outline: none;
            font-size: 16px;
            text-align: center;
        }

        /* Tabs for settings */
        .tab-container {
            width: 100%;
            margin-bottom: 20px;
        }

        .tab-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            border-radius: 20px;
            background: var(--primary-color);
            box-shadow: 3px 3px 6px var(--shadow-color-dark),
                       -3px -3px 6px var(--shadow-color-light);
            overflow: hidden;
        }

        .tab-button {
            flex: 1;
            padding: 15px 10px;
            background: transparent;
            border: none;
            color: var(--text-color);
            font-size: 16px;
            font-weight: normal;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-family: 'Vazir', 'Tahoma', sans-serif;
        }

        .tab-button.active {
            background-color: #757575;
            color: white;
            box-shadow: inset 3px 3px 5px rgba(0, 0, 0, 0.2);
        }

        .tab-button:not(.active):hover {
            background-color: rgba(109, 93, 252, 0.1);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* New recommendation input row */
        .new-recommendation-row {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            width: 100%;
        }

        .recommendation-input {
            flex: 1;
            padding: 12px 15px;
            border-radius: 10px;
            background-color: var(--primary-color);
            box-shadow: inset 3px 3px 5px var(--shadow-color-dark),
                        inset -3px -3px 5px var(--shadow-color-light);
            color: var(--text-color);
            border: none;
            outline: none;
            font-size: 14px;
        }

        .recommendation-title-input {
            flex: 0.3; /* 30% width */
            text-align: center; /* Center align the text */
        }

        .recommendation-text-input {
            flex: 0.7; /* 70% width */
            min-height: 48px; /* Set minimum height */
            line-height: 24px; /* Add consistent line height */
            display: flex;
            align-items: center;
        }

        /* Add styles for placeholder text in the recommendation text input */
        .recommendation-text-input::placeholder {
            line-height: 24px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }

        .add-btn {
            padding: 0 20px;
            min-width: 120px;
            border-radius: 10px;
            background: var(--accent-color);
            color: white;
            border: none;
            font-size: 14px;
            cursor: pointer;
            box-shadow: 3px 3px 6px var(--shadow-color-dark),
                      -3px -3px 6px var(--shadow-color-light);
            transition: all 0.2s ease;
        }

        .add-btn:hover {
            background-color: #5a48d3;
            transform: translateY(-2px);
        }

        .add-btn:active {
            box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3);
            transform: translateY(0);
        }

        /* Modern Arrow Styles - Arrow Head Only */
        .modern-arrow {
            position: relative;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            margin: 0 -3px;
            cursor: pointer;
        }

        /* Main Arrow Head */
        .modern-arrow::before {
            content: '';
            position: absolute;
            left: 15px;
            width: 10px;
            height: 10px;
            border-left: 3px solid #FF6B35;
            border-bottom: 3px solid #FF6B35;
            transform: rotate(45deg);
            border-radius: 0 0 0 2px;
            filter: drop-shadow(0 2px 5px rgba(255, 107, 53, 0.4));
            animation: arrowHeadFlow 2.5s ease-in-out infinite, colorChangeHead 4s ease-in-out infinite;
            transform-origin: center;
        }

        /* Glow Effect Behind Arrow */
        .modern-arrow::after {
            content: '';
            position: absolute;
            left: 13px;
            width: 14px;
            height: 14px;
            border-left: 4px solid rgba(255, 107, 53, 0.3);
            border-bottom: 4px solid rgba(255, 107, 53, 0.3);
            transform: rotate(45deg);
            border-radius: 0 0 0 3px;
            filter: blur(1.5px);
            animation: arrowGlowFlow 2.5s ease-in-out infinite, colorChangeGlow 4s ease-in-out infinite;
            transform-origin: center;
        }



        @keyframes arrowHeadFlow {
            0%, 100% {
                opacity: 0.8;
                transform: rotate(45deg) scale(1);
                filter: drop-shadow(0 2px 5px rgba(255, 107, 53, 0.4));
            }
            50% {
                opacity: 1;
                transform: rotate(45deg) scale(1.15);
                filter: drop-shadow(0 3px 8px rgba(255, 107, 53, 0.6));
            }
        }

        @keyframes arrowGlowFlow {
            0%, 100% {
                opacity: 0.4;
                transform: rotate(45deg) scale(1);
            }
            50% {
                opacity: 0.7;
                transform: rotate(45deg) scale(1.1);
            }
        }



        /* Hover effect for the new arrow design */
        .modern-arrow:hover::before {
            animation-duration: 1.5s;
            filter: drop-shadow(0 4px 15px rgba(255, 59, 48, 0.6));
        }

        .modern-arrow:hover::after {
            animation-duration: 1.5s;
            filter: drop-shadow(0 3px 12px rgba(255, 59, 48, 0.7));
        }

        /* Second Arrow with Delay */
        .modern-arrow.second-arrow::before {
            animation: arrowHeadFlow 2.5s ease-in-out infinite 0.3s, colorChangeHead 4s ease-in-out infinite 0.3s;
        }

        .modern-arrow.second-arrow::after {
            animation: arrowGlowFlow 2.5s ease-in-out infinite 0.3s, colorChangeGlow 4s ease-in-out infinite 0.3s;
        }

        /* Random Color Change Animations - Arrow Head Only */
        @keyframes colorChangeHead {
            0% {
                border-left-color: #FF6B35;
                border-bottom-color: #FF6B35;
                filter: drop-shadow(0 3px 8px rgba(255, 107, 53, 0.4));
            }
            10% {
                border-left-color: #FF3B30;
                border-bottom-color: #FF3B30;
                filter: drop-shadow(0 3px 8px rgba(255, 59, 48, 0.4));
            }
            20% {
                border-left-color: #FF2D92;
                border-bottom-color: #FF2D92;
                filter: drop-shadow(0 3px 8px rgba(255, 45, 146, 0.4));
            }
            30% {
                border-left-color: #AF52DE;
                border-bottom-color: #AF52DE;
                filter: drop-shadow(0 3px 8px rgba(175, 82, 222, 0.4));
            }
            40% {
                border-left-color: #5856D6;
                border-bottom-color: #5856D6;
                filter: drop-shadow(0 3px 8px rgba(88, 86, 214, 0.4));
            }
            50% {
                border-left-color: #007AFF;
                border-bottom-color: #007AFF;
                filter: drop-shadow(0 3px 8px rgba(0, 122, 255, 0.4));
            }
            60% {
                border-left-color: #5AC8FA;
                border-bottom-color: #5AC8FA;
                filter: drop-shadow(0 3px 8px rgba(90, 200, 250, 0.4));
            }
            70% {
                border-left-color: #34C759;
                border-bottom-color: #34C759;
                filter: drop-shadow(0 3px 8px rgba(52, 199, 89, 0.4));
            }
            80% {
                border-left-color: #30D158;
                border-bottom-color: #30D158;
                filter: drop-shadow(0 3px 8px rgba(48, 209, 88, 0.4));
            }
            90% {
                border-left-color: #FF9500;
                border-bottom-color: #FF9500;
                filter: drop-shadow(0 3px 8px rgba(255, 149, 0, 0.4));
            }
            100% {
                border-left-color: #FF6B35;
                border-bottom-color: #FF6B35;
                filter: drop-shadow(0 3px 8px rgba(255, 107, 53, 0.4));
            }
        }

        @keyframes colorChangeGlow {
            0% {
                border-left-color: rgba(255, 107, 53, 0.3);
                border-bottom-color: rgba(255, 107, 53, 0.3);
            }
            10% {
                border-left-color: rgba(255, 59, 48, 0.3);
                border-bottom-color: rgba(255, 59, 48, 0.3);
            }
            20% {
                border-left-color: rgba(255, 45, 146, 0.3);
                border-bottom-color: rgba(255, 45, 146, 0.3);
            }
            30% {
                border-left-color: rgba(175, 82, 222, 0.3);
                border-bottom-color: rgba(175, 82, 222, 0.3);
            }
            40% {
                border-left-color: rgba(88, 86, 214, 0.3);
                border-bottom-color: rgba(88, 86, 214, 0.3);
            }
            50% {
                border-left-color: rgba(0, 122, 255, 0.3);
                border-bottom-color: rgba(0, 122, 255, 0.3);
            }
            60% {
                border-left-color: rgba(90, 200, 250, 0.3);
                border-bottom-color: rgba(90, 200, 250, 0.3);
            }
            70% {
                border-left-color: rgba(52, 199, 89, 0.3);
                border-bottom-color: rgba(52, 199, 89, 0.3);
            }
            80% {
                border-left-color: rgba(48, 209, 88, 0.3);
                border-bottom-color: rgba(48, 209, 88, 0.3);
            }
            90% {
                border-left-color: rgba(255, 149, 0, 0.3);
                border-bottom-color: rgba(255, 149, 0, 0.3);
            }
            100% {
                border-left-color: rgba(255, 107, 53, 0.3);
                border-bottom-color: rgba(255, 107, 53, 0.3);
            }
        }

        @keyframes particleColorChange {
            0% {
                background: radial-gradient(circle, #FFD23F 0%, #F7931E 100%);
            }
            16.66% {
                background: radial-gradient(circle, #FF3B30 0%, #FF9500 100%);
            }
            33.33% {
                background: radial-gradient(circle, #34C759 0%, #30D158 100%);
            }
            50% {
                background: radial-gradient(circle, #007AFF 0%, #0A84FF 100%);
            }
            66.66% {
                background: radial-gradient(circle, #AF52DE 0%, #BF5AF2 100%);
            }
            83.33% {
                background: radial-gradient(circle, #FF9500 0%, #FFCC02 100%);
            }
            100% {
                background: radial-gradient(circle, #FFD23F 0%, #F7931E 100%);
            }
        }

        /* Add snackbar styles after the existing confirmation-message class */
        .confirmation-message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: var(--accent-color);
            color: white;
            padding: 12px 25px;
            border-radius: 50px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            font-size: 14px;
            opacity: 1;
            transition: opacity 0.5s ease;
            z-index: 1100;
        }

        /* Modern Snackbar styles */
        .snackbar {
            visibility: hidden;
            position: fixed;
            top: 20px;
            right: -450px;
            min-width: 320px;
            max-width: 400px;
            padding: 18px 24px;
            border-radius: 16px;
            font-size: 15px;
            font-weight: 500;
            text-align: center;
            color: white;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12),
                        0 2px 8px rgba(0, 0, 0, 0.08);
            z-index: 1100;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .snackbar.show {
            visibility: visible;
            opacity: 1;
            right: 20px;
            transform: translateX(0);
        }

        .snackbar.success {
            background: linear-gradient(135deg,
                rgba(52, 199, 89, 0.95) 0%,
                rgba(48, 176, 199, 0.95) 100%);
            border-color: rgba(52, 199, 89, 0.3);
        }

        .snackbar.error {
            background: linear-gradient(135deg,
                rgba(255, 59, 48, 0.95) 0%,
                rgba(255, 45, 85, 0.95) 100%);
            border-color: rgba(255, 59, 48, 0.3);
        }

        .snackbar.info {
            background: linear-gradient(135deg,
                rgba(10, 132, 255, 0.95) 0%,
                rgba(64, 156, 255, 0.95) 100%);
            border-color: rgba(10, 132, 255, 0.3);
        }

        .snackbar.warning {
            background: linear-gradient(135deg,
                rgba(255, 149, 0, 0.95) 0%,
                rgba(255, 179, 64, 0.95) 100%);
            border-color: rgba(255, 149, 0, 0.3);
        }

        /* Snackbar icon styles */
        .snackbar-icon {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
        }

        .snackbar-message {
            flex: 1;
            line-height: 1.4;
        }

        /* Animation for snackbar entrance */
        @keyframes snackbarSlideIn {
            from {
                transform: translateX(100px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes snackbarSlideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100px);
                opacity: 0;
            }
        }

        /* Hover effect for snackbar */
        .snackbar:hover {
            transform: translateX(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15),
                        0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .delete-btn {
            background: var(--primary-color);
            color: #FF3B30;
            font-size: 12px;
            cursor: pointer;
            padding: 0 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: none;
            transform: none;
        }

        /* حذف دکمه‌های اسکرول (فلش‌های بالا و پایین) از textarea */
        textarea::-webkit-scrollbar-button {
            display: none; /* برای مرورگرهای بر پایه WebKit مانند Chrome و Safari */
        }

        textarea {
            overflow: auto; /* برای سایر مرورگرها */
            scrollbar-width: none; /* برای Firefox */
        }

        textarea::-webkit-scrollbar {
            display: none; /* حذف کامل اسکرول‌بارها */
        }

        /* New style for recommendations without container */
        .setting-section-recommendations {
            direction: rtl;
            margin-bottom: 15px;
            max-height: 500px;
            overflow-y: auto;
        }

        /* Add styles for the recommendations container to make it scrollable */
        #recommendationsContainer {
            max-height: 350px;
            overflow-y: auto;
            margin-top: 15px;
            padding-right: 5px;
            border-radius: 10px;
            box-shadow: inset 3px 3px 6px var(--shadow-color-dark),
                      inset -3px -3px 6px var(--shadow-color-light);
            padding: 10px 5px;
            /* Hide scrollbar but keep scrolling functionality */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* Hide WebKit scrollbar (Chrome, Safari, Opera) */
        #recommendationsContainer::-webkit-scrollbar {
            display: none;
        }

        /* Expert Recommendation Dropdown Button */
        .dropdown-container {
            position: relative;
            width: 100%;
        }

        /* Equal width buttons fix */
        .buttons-row {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            margin-top: 15px;
            align-items: center;
        }

        .button-wrapper {
            flex: 1;
            display: flex;
        }

        .button-wrapper .btn {
            width: 100% !important;
            margin: 0 !important;
            flex: 1;
            box-sizing: border-box;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            width: 100%;
            background-color: var(--primary-color);
            border-radius: 40px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
            direction: rtl;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            padding: 10px 0;
            text-align: center;
        }

        .dropdown-content::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        .dropdown-content button {
            display: block;
            width: calc(100% - 20px);
            padding: 12px 0;
            margin: 2px auto;
            border: none;
            background: none;
            text-align: center;
            color: var(--text-color);
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.2s;
            font-family: 'Vazir', 'Tahoma', sans-serif;
            border-radius: 40px;
            font-weight: normal;
            line-height: 1;
        }

        .dropdown-content button:last-child {
            border-bottom: none;
        }

        .dropdown-content button:hover {
            background-color: rgba(66, 133, 244, 0.1);
            border-radius: 40px;
        }

        /* Inquiry dropdown specific styling with rounded corners */
        .dropdown-content.inquiry-dropdown {
            border-radius: 40px;
            padding: 10px 0;
            text-align: center;
        }

        .dropdown-content.inquiry-dropdown button {
            border-radius: 40px;
            margin: 2px auto;
            font-size: 16px;
            color: var(--text-color);
            font-weight: normal;
            text-align: center;
            display: block;
            width: calc(100% - 20px);
            padding: 12px 0;
            border: none;
            background: none;
            cursor: pointer;
            transition: background-color 0.2s;
            font-family: 'Vazir', 'Tahoma', sans-serif;
            line-height: 1;
        }

        .dropdown-content.inquiry-dropdown button:hover {
            background-color: rgba(66, 133, 244, 0.1);
            border-radius: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="text-align: center; margin-bottom: 5px;">
            <img src="Naft Logo.png" alt="لوگوی نفت" style="max-height: 90px; margin: 0 auto;">
        </div>
        <h1 style="font-size: 24px; margin-top: 0;">ایجاد فرم ارزیابی فنی و مالی</h1>

        <!-- کادر مستطیلی با گوشه‌های گرد که همه المان‌ها را در بر می‌گیرد -->
        <div style="padding: 25px 25px 15px; margin-bottom: 20px; border: 1px solid var(--shadow-color-dark); border-radius: 15px; background: transparent;">
            <textarea id="inputText" class="text-area" placeholder="متن کپی شده از سامانه را در این قسمت قرار دهید ..."></textarea>

            <div class="controls-row" style="margin-bottom: 10px;">
                <div class="input-control need-label">
                    <input type="text" id="needNumber" placeholder="شماره نیاز">
                </div>

                <div class="input-control purchase-label">
                    <input type="text" id="refPurchaseNumber" placeholder="شماره درخواست خرید">
                </div>

                <div class="input-control count-label">
                    <input type="text" id="receivedItemsCount" placeholder="تعداد شرکت ها">
                </div>
            </div>

            <div class="info-text" style="text-align: center; margin: 6px 0 5px; color: var(--text-color); font-size: 14px;">
                اطلاعات استخراج شده‌ از متن، در فیلدهای بالا نمایش داده میشود.
            </div>

        </div>

        <!-- Buttons row container -->
        <div class="buttons-row">
            <div class="button-wrapper">
                <button id="processBtn" class="btn normal-weight" style="border: 1px solid #aaaaaa;">استخراج اطلاعات</button>
            </div>

            <!-- Modern Arrow -->
            <div class="modern-arrow">
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
            </div>

            <!-- Inquiry Count Dropdown Button -->
            <div class="button-wrapper">
                <div class="dropdown-container" id="inquiryDropdownContainer">
                    <button id="inquiryCountBtn" class="btn normal-weight" style="border: 1px solid #aaaaaa; margin: 0;">
                        <span id="inquiryCountText">دفعات استعلام‌گیری</span>
                        <input type="hidden" id="inquiryCount" value="">
                    </button>
                    <div id="inquiryDropdown" class="dropdown-content inquiry-dropdown">
                        <!-- Inquiry count options will be added here dynamically -->
                    </div>
                </div>
            </div>

            <!-- Modern Arrow -->
            <div class="modern-arrow">
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
            </div>

            <!-- Expert Recommendation Dropdown Button -->
            <div class="button-wrapper">
                <div class="dropdown-container" id="recommendationDropdownContainer">
                    <button id="recommendationBtn" class="btn normal-weight" style="border: 1px solid #aaaaaa;">پیشنهاد کارشناس خرید</button>
                    <div id="recommendationDropdown" class="dropdown-content">
                        <!-- Recommendation options will be added here dynamically -->
                    </div>
                </div>
            </div>

            <!-- Second Modern Arrow -->
            <div class="modern-arrow second-arrow">
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
            </div>

            <div class="button-wrapper">
                <button id="createFormBtn" class="btn normal-weight" style="background: linear-gradient(135deg, #0A84FF 0%, #0055CC 100%); color: white; border: 1px solid #aaaaaa;">ایجاد فرم ارزیابی</button>
            </div>
        </div>

        <table id="companiesTable" class="hidden">
            <thead>
                <tr class="header-row">
                    <th colspan="4" style="padding: 0;">
                        <div class="header-unified">
                            <div class="header-cell">ردیف</div>
                            <div class="header-cell">نام تامین کننده</div>
                            <div class="header-cell">قیمت کل پیشنهاد شده</div>
                            <div class="header-cell">تایید / عدم تایید</div>
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody id="companiesTableBody">
                <!-- Supplier data will be added here -->
            </tbody>
        </table>
    </div>

    <!-- Settings button -->
    <div class="settings-btn" id="settingsBtn">
        <div class="settings-icon"></div>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="close-btn" id="closeSettingsBtn">بستن</div>
            <button id="saveSettingsBtn" class="save-btn">ذخیره تنظیمات</button>
            <h2 style="text-align: center; margin-bottom: 20px; margin-top: 20px;">تنظیمات</h2>

            <div class="tab-container">
                <div class="tab-header">
                    <button class="tab-button active" data-tab="officials">مسئولین</button>
                    <button class="tab-button" data-tab="transaction">سقف معاملات</button>
                    <button class="tab-button" data-tab="recommendations">پیشنهادات</button>
                </div>

                <!-- Officials Tab Content -->
                <div id="officials-tab" class="tab-content active">
                    <div style="display: flex; flex-direction: column; align-items: center; gap: 15px;">
                        <input type="text" id="purchasingExpert" class="settings-input" placeholder="کارشناس خرید" style="width: 60%;">
                        <input type="text" id="purchasingManager" class="settings-input" placeholder="رئیس خرید" style="width: 60%;">
                        <input type="text" id="goodsUnitManager" class="settings-input" placeholder="رئیس واحد کالا" style="width: 60%;">
                    </div>
                </div>

                <!-- Transaction Limit Tab Content -->
                <div id="transaction-tab" class="tab-content">
                    <div style="display: flex; flex-direction: column; align-items: center; gap: 15px;">
                        <input type="text" id="transactionLimit" class="settings-input" placeholder="مبلغ سقف معاملات متوسط را وارد کرده و دکمه اینتر را فشار دهید" style="width: 60%;">
                    </div>
                </div>

                <!-- Expert Recommendations Tab Content -->
                <div id="recommendations-tab" class="tab-content">
                    <!-- New input row for adding recommendations -->
                    <div class="new-recommendation-row">
                        <input type="text" id="newRecommendationTitle" class="recommendation-input recommendation-title-input" placeholder="عنوان پیشنهاد">
                        <textarea id="newRecommendationText" class="recommendation-input recommendation-text-input" placeholder="متن پیشنهاد" style="resize: vertical; height: 48px; overflow-y: hidden; padding: 12px 15px; display: block; line-height: 24px;"></textarea>
                        <button id="addRecommendationBtn" class="add-btn" style="background: linear-gradient(135deg, #00a65a 0%, #006400 100%); transform: none; transition: none;">افزودن</button>
                    </div>

                    <!-- Recommendations will be added here directly in a scrollable container -->
                    <div id="recommendationsContainer">
                    </div>
                </div>
            </div>

            <!-- Removed the save settings button from bottom -->
        </div>
    </div>

    <!-- Snackbar container -->
    <div id="snackbar" class="snackbar"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const processBtn = document.getElementById('processBtn');
            const inputText = document.getElementById('inputText');
            const needNumber = document.getElementById('needNumber');
            const refPurchaseNumber = document.getElementById('refPurchaseNumber');
            const receivedItemsCount = document.getElementById('receivedItemsCount');
            const inquiryCount = document.getElementById('inquiryCount');
            const companiesTableBody = document.getElementById('companiesTableBody');
            const companiesTable = document.getElementById('companiesTable');

            // Expert recommendation elements
            const recommendationDropdownContainer = document.getElementById('recommendationDropdownContainer');
            const recommendationBtn = document.getElementById('recommendationBtn');
            const recommendationDropdown = document.getElementById('recommendationDropdown');

            // Inquiry count dropdown elements
            const inquiryDropdownContainer = document.getElementById('inquiryDropdownContainer');
            const inquiryCountBtn = document.getElementById('inquiryCountBtn');
            const inquiryDropdown = document.getElementById('inquiryDropdown');

            // Settings elements
            const settingsBtn = document.getElementById('settingsBtn');
            const settingsModal = document.getElementById('settingsModal');
            const closeSettingsBtn = document.getElementById('closeSettingsBtn');
            const saveSettingsBtn = document.getElementById('saveSettingsBtn');
            const purchasingExpert = document.getElementById('purchasingExpert');
            const purchasingManager = document.getElementById('purchasingManager');
            const goodsUnitManager = document.getElementById('goodsUnitManager');
            const transactionLimit = document.getElementById('transactionLimit');
            const recommendationsContainer = document.getElementById('recommendationsContainer');
            const addRecommendationBtn = document.getElementById('addRecommendationBtn');

            // New button for creating form
            const createFormBtn = document.getElementById('createFormBtn');

            // Initialize recommendations from localStorage or use default if none exists
            const recommendations = JSON.parse(localStorage.getItem('recommendations') || '[]');

            // Add default recommendations if the array is empty
            if (recommendations.length === 0) {
                const defaultRecommendations = [
                    {
                        title: "خريد از برنده",
                        text: "با توجه به ارزيابی فنی و مالی صورت گرفته ، پيشنهاد می گردد خريد از شركت x با مبلغ y ریال به دليل ارايه نازلترين قيمت صورت پذيرد. اين خريد با رعايت مفاد حداكثر استفاده از توان توليدی و خدماتی كشور و حمايت از كالای ايرانی مصوب 98/02/15 به ويژه مواد 3،5 و 16 صورت می پذيرد."
                    },
                    {
                        title: "عدم دريافت پاسخ",
                        text: "با توجه به عدم دريافت پاسخ از طرف تامين‌كنندگان ؛ پيشنهاد می گردد نياز فوق ابطال و تقاضا مجددا در سامانه بارگزاری گردد ."
                    },
                    {
                        title: "عدم تائيد پاسخ های دريافتی",
                        text: "با توجه به عدم تائيد پاسخ های دريافتی از طرف تامين كنندگان ؛ پيشنهاد می گردد نياز فوق ابطال و تقاضا مجددا در سامانه بارگزاری گردد ."
                    },
                    {
                        title: "انقضای مهلت اعلام آمادگی",
                        text: "با توجه به انقضای مهلت اعلام آمادگی شركت تامين كننده ؛  پيشنهاد می گردد نياز فوق ابطال و تقاضا مجددا در سامانه بارگزاری گردد ."
                    },
                    {
                        title: "اعلام عدم آمادگی",
                        text: "با توجه به اعلام عدم آمادگی شركت برنده ؛  پيشنهاد می گردد نياز فوق ابطال و تقاضا مجددا در سامانه بارگزاری گردد ."
                    },
                    {
                        title: "پيشنهاد قيمت عمده",
                        text: "با توجه به ارايه قيمتهای نامتعارف ( در حد معاملات عمده ) از طرف تامين كنندگان در چندبن بار استعلام صورت گرفته ، پيشنهاد می گردد قیمت برآوردی تقاضا تقاضا مجددا مورد بازبینی قرار گرفته و پس از آن در خصوص ادامه فرایند خرید تصمیم گیری شود."
                    },
                    {
                        title: "ابطال تقاضا و ايجاد تقاضای جديد",
                        text: "با توجه به چندين بار استعلام و عدم حصول نتيجه، پيشنهاد ميگردد تقاضا ابطال و به متقاضی اعلام گردد در صورت نياز به كالا با تهيه مستندات و اطلاعات دقيق تر نسبت به ايجاد تقاضای جديد اقدام نمايد."
                    },
                    {
                        title: "اصلاح تقاضا حين فرايند خريد",
                        text: "با توجه به درخواست متقاضی جهت اصلاح تقاضا، پيشنهاد ميگردد نياز ابطال و تقاضا پس از درج اصلاحات لازم مجددا در سامانه ستاد اعلان نياز گردد."
                    },
                    {
                        title: "ابطال سفارش ابلاغ شده",
                        text: "با توجه به ناتوانی و انصراف شركت برنده در تامين كالای درخواستی، پيشنهاد ميگردد سفارش ابطال و تقاضا مجددا در سامانه ستاد اعلان نياز گردد."
                    }
                ];

                // Add default recommendations to the array
                recommendations.push(...defaultRecommendations);

                // Save to localStorage
                localStorage.setItem('recommendations', JSON.stringify(recommendations));
            }

            // Function to convert Persian/Arabic numbers to English numbers
            function convertPersianToEnglishNumbers(text) {
                const persianNumbers = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
                const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
                const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

                let result = text;

                // Replace Persian numbers with English numbers
                for (let i = 0; i < 10; i++) {
                    const persianRegex = new RegExp(persianNumbers[i], 'g');
                    const arabicRegex = new RegExp(arabicNumbers[i], 'g');
                    result = result.replace(persianRegex, englishNumbers[i]);
                    result = result.replace(arabicRegex, englishNumbers[i]);
                }

                return result;
            }

            // Function to convert English numbers to Persian numbers
            function convertEnglishToPersianNumbers(text) {
                const persianNumbers = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
                const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

                let result = String(text);

                // Replace English numbers with Persian numbers
                for (let i = 0; i < 10; i++) {
                    const englishRegex = new RegExp(englishNumbers[i], 'g');
                    result = result.replace(englishRegex, persianNumbers[i]);
                }

                return result;
            }

            // Function to format numbers with thousand separators
            function formatNumberWithSeparators(num) {
                // Convert to string and remove any non-digit characters except for decimal points
                let numStr = String(num).replace(/[^\d\.]/g, '');

                // Split by decimal point if any
                let parts = numStr.split('.');

                // Format the integer part with thousand separators
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');

                // Join back with decimal part if exists
                return parts.join('.');
            }

            // Format price to add "ریال" and convert to Persian numbers
            function formatPrice(price) {
                // Handle null, undefined, empty string, or explicit "-" placeholder
                if (!price || String(price).trim() === '' || String(price).trim() === '-') {
                    return '-';
                }

                let workingString = String(price);

                // 1. Remove "ریال" suffix
                workingString = workingString.replace(/\s*ریال\s*$/, '').trim();

                // If it was just " ریال" or "- ریال" or became empty/placeholder after stripping suffix
                if (workingString === '' || workingString === '-') {
                    return '-';
                }

                // 2. Convert all numerals in the string to English. This handles Persian/Arabic digits.
                workingString = convertPersianToEnglishNumbers(workingString); // e.g., "۱,۲۳۴" becomes "1,234"

                // 3. Remove any characters that are not English digits or a decimal point.
                // This will effectively remove all commas (Persian or English that might have passed through step 2).
                workingString = workingString.replace(/[^\d\.]/g, ''); // e.g., "1,234" becomes "1234"

                // If after all cleaning, the string is empty (e.g., input was "abc ریال"), return '-'
                if (workingString === '') {
                    return '-';
                }

                // 4. Format with thousand separators. formatNumberWithSeparators expects clean English digits.
                let formattedEngNum = formatNumberWithSeparators(workingString); // e.g., "1234" becomes "1,234"

                // 5. Convert the formatted English number string back to Persian numbers for display
                let persianFormattedNum = convertEnglishToPersianNumbers(formattedEngNum); // e.g., "1,234" becomes "۱,۲۳۴"

                // 6. Add "ریال" suffix
                return persianFormattedNum + ' ریال';
            }

            // Function to render recommendations in the container
            function renderRecommendations() {
                const container = document.getElementById('recommendationsContainer');
                container.innerHTML = '';

                recommendations.forEach((rec, index) => {
                    const item = document.createElement('div');
                    item.className = 'recommendation-item';
                    item.dataset.index = index;

                    // Title element
                    const title = document.createElement('div');
                    title.className = 'recommendation-title';
                    title.textContent = rec.title;

                    // Text element
                    const text = document.createElement('div');
                    text.className = 'recommendation-text';
                    text.textContent = rec.text;
                    text.style.whiteSpace = "pre-line"; // Preserve line breaks
                    text.style.textAlign = "right";

                    // Actions container
                    const actions = document.createElement('div');
                    actions.className = 'recommendation-actions';

                    // Edit button
                    const editBtn = document.createElement('button');
                    editBtn.className = 'edit-btn';
                    editBtn.innerHTML = 'ویرایش';
                    editBtn.addEventListener('click', () => {
                        // Set form fields with the recommendation data
                        document.getElementById('newRecommendationTitle').value = rec.title;
                        const textArea = document.getElementById('newRecommendationText');
                        textArea.value = rec.text;

                        // Adjust textarea height to fit content
                        adjustTextareaHeight(textArea);

                        // Change the add button to save
                        const addBtn = document.getElementById('addRecommendationBtn');
                        addBtn.textContent = 'ذخیره';
                        addBtn.style.background = 'linear-gradient(135deg, #1976d2 0%, #0d47a1 100%)';
                        addBtn.dataset.editIndex = index;

                        // Scroll to the form
                        document.getElementById('newRecommendationTitle').scrollIntoView({ behavior: 'smooth' });

                        // Focus on title field
                        document.getElementById('newRecommendationTitle').focus();
                    });

                    // Delete button
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'delete-btn';
                    deleteBtn.innerHTML = 'حذف';
                    deleteBtn.addEventListener('click', () => {
                        const deletedIndex = index;
                        recommendations.splice(deletedIndex, 1);
                        localStorage.setItem('recommendations', JSON.stringify(recommendations));

                        // Show delete success message
                        showSnackbar('پیشنهاد با موفقیت حذف شد', 'info');

                        // Check if the deleted item was selected in the dropdown
                        const selectedIndex = parseInt(recommendationBtn.dataset.selectedIndex);
                        if (selectedIndex === deletedIndex) {
                            // Reset the recommendation button text and selected index
                            recommendationBtn.textContent = "پیشنهاد کارشناس خرید";
                            recommendationBtn.dataset.selectedIndex = -1;
                        } else if (selectedIndex > deletedIndex) {
                            // Adjust the selected index as items shifted
                            recommendationBtn.dataset.selectedIndex = selectedIndex - 1;
                        }

                        // Check if we were editing this item
                        if (document.getElementById('addRecommendationBtn').dataset.editIndex == deletedIndex) {
                            // Reset the form and button
                            document.getElementById('newRecommendationTitle').value = '';
                            document.getElementById('newRecommendationText').value = '';
                            document.getElementById('addRecommendationBtn').textContent = 'افزودن';
                            document.getElementById('addRecommendationBtn').style.background = 'linear-gradient(135deg, #00a65a 0%, #006400 100%)';
                            delete document.getElementById('addRecommendationBtn').dataset.editIndex;
                        } else if (document.getElementById('addRecommendationBtn').dataset.editIndex > deletedIndex) {
                            // Adjust the edit index as items shifted
                            document.getElementById('addRecommendationBtn').dataset.editIndex = document.getElementById('addRecommendationBtn').dataset.editIndex - 1;
                        }

                        // Update the recommendations display
                        renderRecommendations();

                        // Immediately update the dropdown content
                        populateRecommendationDropdown();
                    });

                    // Add elements to the item
                    actions.appendChild(editBtn);
                    actions.appendChild(deleteBtn);

                    item.appendChild(title);
                    item.appendChild(text);
                    item.appendChild(actions);
                    container.appendChild(item);
                });
            }

            // Add recommendation button click handler
            document.getElementById('addRecommendationBtn').addEventListener('click', () => {
                const titleInput = document.getElementById('newRecommendationTitle');
                const textInput = document.getElementById('newRecommendationText');

                const title = titleInput.value.trim();
                const text = textInput.value.trim();

                if (title && text) {
                    const editIndex = document.getElementById('addRecommendationBtn').dataset.editIndex;

                    if (editIndex !== undefined) {
                        // Update existing recommendation
                        recommendations[editIndex] = {
                            title: title,
                            text: text
                        };

                        // Reset button text and remove edit index
                        document.getElementById('addRecommendationBtn').textContent = 'افزودن';
                        document.getElementById('addRecommendationBtn').style.background = 'linear-gradient(135deg, #00a65a 0%, #006400 100%)';
                        delete document.getElementById('addRecommendationBtn').dataset.editIndex;

                        // Show success message
                        showSnackbar('پیشنهاد با موفقیت ویرایش شد', 'success');
                    } else {
                        // Add new recommendation
                        recommendations.push({
                            title: title,
                            text: text
                        });

                        // Show success message
                        showSnackbar('پیشنهاد جدید با موفقیت افزوده شد', 'success');
                    }

                    localStorage.setItem('recommendations', JSON.stringify(recommendations));

                    // Clear input fields
                    titleInput.value = '';
                    textInput.value = '';

                    // Reset textarea height to default explicitly
                    textInput.style.height = '48px';

                    // Update the display
                    renderRecommendations();

                    // Immediately update the dropdown content
                    populateRecommendationDropdown();
                } else {
                    // Show error message if fields are empty
                    showSnackbar('لطفا عنوان و متن پیشنهاد را وارد کنید', 'error');
                }
            });

            // Function to automatically adjust textarea height
            function adjustTextareaHeight(textarea) {
                // Reset height to auto first to get accurate scrollHeight
                textarea.style.height = 'auto';
                // Set the height to match the scrollHeight (content height), but never less than the original height
                const minHeight = 48; // Match the min-height value in CSS
                textarea.style.height = Math.max(textarea.scrollHeight, minHeight) + 'px';
            }

            // Remove the input event listener to prevent auto-adjusting height
            // document.getElementById('newRecommendationText').addEventListener('input', function() {
            //     adjustTextareaHeight(this);
            // });

            // Initial render of recommendations
            renderRecommendations();

            // Initialize default recommendation button text
            recommendationBtn.dataset.selectedIndex = -1;

            // Function to adjust font size based on text length
            function adjustButtonFontSize(button, text) {
                // Reset to default font size first
                button.style.fontSize = '16px';
                button.textContent = text;

                // Check if text overflows
                const buttonWidth = button.offsetWidth;
                const textWidth = button.scrollWidth;

                if (textWidth > buttonWidth) {
                    // Calculate appropriate font size
                    const ratio = buttonWidth / textWidth;
                    let newFontSize = Math.max(10, Math.floor(16 * ratio * 0.9)); // Minimum 10px, with 10% margin
                    button.style.fontSize = newFontSize + 'px';
                }
            }

            // Function to populate the recommendation dropdown
            function populateRecommendationDropdown() {
                recommendationDropdown.innerHTML = '';

                recommendations.forEach((rec, index) => {
                    const option = document.createElement('button');
                    option.textContent = rec.title;
                    option.dataset.index = index;

                    option.addEventListener('click', function() {
                        recommendationBtn.textContent = rec.title;
                        recommendationBtn.dataset.selectedIndex = index;

                        // Adjust font size if text is too long
                        setTimeout(() => {
                            adjustButtonFontSize(recommendationBtn, rec.title);
                        }, 10);

                        hideRecommendationDropdown();
                    });

                    recommendationDropdown.appendChild(option);
                });
            }

            // Function to show recommendation dropdown
            function showRecommendationDropdown() {
                recommendationDropdown.style.display = 'block';
            }

            // Function to hide recommendation dropdown
            function hideRecommendationDropdown() {
                recommendationDropdown.style.display = 'none';
            }

            // Toggle dropdown when clicking on the button
            recommendationBtn.addEventListener('click', function(e) {
                e.preventDefault();
                if (recommendationDropdown.style.display === 'block') {
                    hideRecommendationDropdown();
                } else {
                    showRecommendationDropdown();
                }
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!recommendationBtn.contains(e.target) && !recommendationDropdown.contains(e.target)) {
                    hideRecommendationDropdown();
                }
                if (!inquiryCountBtn.contains(e.target) && !inquiryDropdown.contains(e.target)) {
                    hideInquiryDropdown();
                }
            });

            // Function to populate the inquiry dropdown with numbers 1-10
            function populateInquiryDropdown() {
                inquiryDropdown.innerHTML = '';

                for (let i = 1; i <= 10; i++) {
                    const option = document.createElement('button');
                    option.textContent = convertEnglishToPersianNumbers(i.toString());
                    option.dataset.value = i;

                    option.addEventListener('click', function() {
                        document.getElementById('inquiryCount').value = i;
                        document.getElementById('inquiryCountText').textContent = convertEnglishToPersianNumbers(i.toString());
                        hideInquiryDropdown();
                    });

                    inquiryDropdown.appendChild(option);
                }
            }

            // Function to show inquiry dropdown
            function showInquiryDropdown() {
                inquiryDropdown.style.display = 'block';
            }

            // Function to hide inquiry dropdown
            function hideInquiryDropdown() {
                inquiryDropdown.style.display = 'none';
            }

            // Toggle inquiry dropdown when clicking on the button
            inquiryCountBtn.addEventListener('click', function(e) {
                e.preventDefault();
                if (inquiryDropdown.style.display === 'block') {
                    hideInquiryDropdown();
                } else {
                    showInquiryDropdown();
                }
            });

            // Initialize inquiry dropdown
            populateInquiryDropdown();

            // Add Persian number formatting to transaction limit
            transactionLimit.addEventListener('focus', function() {
                // Remove riyal suffix and any thousand separators
                this.value = this.value.replace(/\s*ریال\s*$/, '').trim().replace(/,/g, '');
            });

            transactionLimit.addEventListener('input', function() {
                // First convert any Persian/Arabic numbers to English numbers
                let value = convertPersianToEnglishNumbers(this.value);

                // Remove non-digits
                value = value.replace(/[^\d]/g, '');

                if (value) {
                    this.value = formatNumberWithSeparators(value);
                }
            });

            transactionLimit.addEventListener('blur', function() {
                // Format fully when field loses focus
                if (this.value) {
                    this.value = formatPrice(this.value);
                }
            });

            // Add Enter key handler for transaction limit
            transactionLimit.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault(); // Prevent form submission

                    // Apply the same formatting logic as the blur event
                    if (this.value) {
                        this.value = formatPrice(this.value);
                    }

                    // Keep focus in the field
                    this.focus();
                }
            });

            // Number formatting for inquiry count
            inquiryCount.addEventListener('change', function() {
                // Convert to Persian numbers
                this.value = convertEnglishToPersianNumbers(convertPersianToEnglishNumbers(this.value));
            });

            // Function to load settings from localStorage
            function loadSettings() {
                const settings = JSON.parse(localStorage.getItem('appSettings') || '{}');

                purchasingExpert.value = settings.purchasingExpert || '';
                purchasingManager.value = settings.purchasingManager || '';
                goodsUnitManager.value = settings.goodsUnitManager || '';
                transactionLimit.value = settings.transactionLimit ? formatPrice(settings.transactionLimit) : '';

                // Removed loading inquiryCount from settings as it's not part of settings
            }

            // Function to save settings to localStorage
            function saveSettings() {
                const settings = {
                    purchasingExpert: purchasingExpert.value,
                    purchasingManager: purchasingManager.value,
                    goodsUnitManager: goodsUnitManager.value,
                    transactionLimit: convertPersianToEnglishNumbers(transactionLimit.value.replace(/[^\d,\u06F0-\u06F9]/g, '')),
                };

                localStorage.setItem('appSettings', JSON.stringify(settings));

                populateRecommendationDropdown();
                hideRecommendationDropdown();

                const selectedIndex = parseInt(recommendationBtn.dataset.selectedIndex);
                if (selectedIndex >= 0 && (selectedIndex >= recommendations.length ||
                    (recommendations[selectedIndex] &&
                     recommendations[selectedIndex].title !== recommendationBtn.textContent))) {
                    recommendationBtn.textContent = "پیشنهاد کارشناس خرید";
                    recommendationBtn.dataset.selectedIndex = -1;
                }

                // Show a success message using the snackbar instead of creating a new element
                showSnackbar('تنظیمات با موفقیت ذخیره شد', 'success');

                // Close the modal
                settingsModal.style.display = 'none';
                document.body.classList.remove('modal-open');
            }

            // Settings modal event listeners
            settingsBtn.addEventListener('click', function() {
                settingsModal.style.display = 'block';
                document.body.classList.add('modal-open'); // Add class to body when modal opens

                // Always set the officials tab as active when opening settings
                // Remove active class from all tabs and content
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

                // Set officials tab as active
                document.querySelector('.tab-button[data-tab="officials"]').classList.add('active');
                document.getElementById('officials-tab').classList.add('active');
            });

            closeSettingsBtn.addEventListener('click', function() {
                settingsModal.style.display = 'none';
                document.body.classList.remove('modal-open'); // Remove class when modal closes
            });

            // Removing this event listener to prevent the modal from closing when clicking outside
            // window.addEventListener('click', function(event) {
            //     if (event.target === settingsModal) {
            //         settingsModal.style.display = 'none';
            //     }
            // });

            saveSettingsBtn.addEventListener('click', saveSettings);

            // Tab switching functionality
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to current button
                    this.classList.add('active');

                    // Show the corresponding tab content
                    const tabId = this.getAttribute('data-tab') + '-tab';
                    document.getElementById(tabId).classList.add('active');
                });
            });

            processBtn.addEventListener('click', function() {
                const rawText = inputText.value;
                if (!rawText.trim()) {
                    showSnackbar('لطفا متنی را وارد کنید!', 'error');
                    return;
                }

                // Convert Persian/Arabic numbers to English numbers
                const text = convertPersianToEnglishNumbers(rawText);

                // Extract need number and purchase reference number using patterns
                let needNumberValue = '-';
                let refPurchaseValue = '-';

                // Pattern for need number: Find "شماره نياز" followed by a number on the next line
                const needNumberPattern = /شماره\s*نياز\s*[\n\r]+\s*([0-9\-]+)/i;
                const needMatch = text.match(needNumberPattern);
                if (needMatch && needMatch[1]) {
                    needNumberValue = needMatch[1].trim();
                }

                // Pattern for reference purchase: Find "شماره درخواست خريد مرجع" followed by a number/text on the next line
                const refPurchasePattern = /شماره\s*درخواست\s*خريد\s*مرجع\s*[\n\r]+\s*([0-9A-Za-z\-]+)/i;
                const refMatch = text.match(refPurchasePattern);
                if (refMatch && refMatch[1]) {
                    refPurchaseValue = refMatch[1].trim();
                }

                // If the above patterns don't work, try alternate patterns
                if (needNumberValue === '-') {
                    const altNeedPattern = /شماره\s*نياز[:\t\s]*([0-9\-]+)/i;
                    const altNeedMatch = text.match(altNeedPattern);
                    if (altNeedMatch && altNeedMatch[1]) {
                        needNumberValue = altNeedMatch[1].trim();
                    }
                }

                if (refPurchaseValue === '-') {
                    const altRefPattern = /شماره\s*درخواست\s*خريد\s*مرجع[:\t\s]*([0-9A-Za-z\-]+)/i;
                    const altRefMatch = text.match(altRefPattern);
                    if (altRefMatch && altRefMatch[1]) {
                        refPurchaseValue = altRefMatch[1].trim();
                    }
                }

                // Update the UI with Persian numbers
                needNumber.value = convertEnglishToPersianNumbers(needNumberValue);
                refPurchaseNumber.value = convertEnglishToPersianNumbers(refPurchaseValue);

                // Extract received items count
                let receivedCount = '-';
                // Try to find patterns like "تعداد موارد دریافتی: X" or "X مورد دریافت شد" or similar patterns
                const countPatterns = [
                    /تعداد\s*موارد\s*دریافتی\s*[:：\s]\s*(\d+)/i,
                    /تعداد\s*[:：\s]\s*(\d+)/i,
                    /(\d+)\s*مورد\s*دریافت/i,
                    /دریافت\s*(\d+)\s*مورد/i,
                    /(\d+)\s*نتیجه/i
                ];

                for (const pattern of countPatterns) {
                    const match = text.match(pattern);
                    if (match && match[1]) {
                        receivedCount = match[1].trim();
                        break;
                    }
                }

                // If we couldn't find a specific count pattern, use the number of suppliers found
                if (receivedCount === '-') {
                    // Find the count from suppliers extraction logic below
                    // This will be updated later after suppliers are extracted
                }

                // Split text into lines
                const lines = text.split('\n');

                // Extract suppliers data
                const suppliers = [];

                // Process each line
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line) continue;

                    // Skip header line
                    if (line.includes('رديف') && line.includes('نام تامين کننده') && line.includes('قيمت کل پيشنهاد شده')) {
                        continue;
                    }

                    // Split the line by tabs or multiple spaces
                    const parts = line.split(/\t|(?:\s{2,})/);

                    // Filter out empty parts
                    const filteredParts = parts.filter(part => part.trim());

                    // Try to extract row, name, and price
                    if (filteredParts.length >= 3) {
                        // Assume first part is row number
                        const rowNumber = filteredParts[0].trim();

                        // Assume second part is supplier name
                        const supplierName = filteredParts[1].trim();

                        // Assume third part is price
                        let price = filteredParts[2].trim();

                        // Validate that rowNumber is numeric
                        if (/^\d+$/.test(rowNumber)) {
                            suppliers.push({
                                row: rowNumber,
                                name: supplierName,
                                price: price
                            });
                        }
                    }
                }

                // If we couldn't extract proper data with the above method, try another approach
                if (suppliers.length === 0) {
                    // Try to find rows with the following pattern: number followed by text followed by number
                    const rowPattern = /^\s*(\d+)\s+([\u0600-\u06FF\s\d\w]+?)\s+([\d,]+(?:\.\d+)?)/;

                    for (let i = 0; i < lines.length; i++) {
                        const match = lines[i].match(rowPattern);
                        if (match) {
                            suppliers.push({
                                row: match[1].trim(),
                                name: match[2].trim(),
                                price: match[3].trim()
                            });
                        }
                    }
                }

                // If receivedCount is still not found, use the suppliers count
                if (receivedCount === '-' && suppliers.length > 0) {
                    receivedCount = suppliers.length.toString();
                }

                // Update the received items count field with Persian numbers
                receivedItemsCount.value = convertEnglishToPersianNumbers(receivedCount);

                // Render suppliers in table
                companiesTableBody.innerHTML = '';

                suppliers.forEach((supplier) => {
                    const row = document.createElement('tr');

                    const rowNumCell = document.createElement('td');
                    rowNumCell.textContent = convertEnglishToPersianNumbers(supplier.row);

                    const nameCell = document.createElement('td');
                    nameCell.textContent = supplier.name;

                    const priceCell = document.createElement('td');
                    const priceInput = document.createElement('input');
                    priceInput.type = 'text';
                    priceInput.className = 'editable-input';
                    priceInput.value = formatPrice(supplier.price);
                    priceInput.addEventListener('change', function() {
                        let inputValue = this.value;

                        // 1. Convert to English to get a base for numeric conversion (handles Persian/Arabic)
                        let englishValue = convertPersianToEnglishNumbers(inputValue);

                        // 2. Remove "ریال" suffix if present (it might be if user didn't fully clear it or from blur)
                        englishValue = englishValue.replace(/\s*ریال\s*$/, '').trim();

                        // 3. Remove all non-digits (like commas) to get the pure number string
                        let cleanNumericString = englishValue.replace(/[^\d\.]/g, '');

                        // Store this clean English numeric string in supplier.price
                        // If the cleaning results in an empty string (e.g., input was just "ریال" or "abc"),
                        // store it as empty or a placeholder like '-' if preferred. For now, empty string.
                        supplier.price = cleanNumericString;

                        // Format the display value using the new robust formatPrice
                        // Pass the clean English numeric string to formatPrice
                        this.value = formatPrice(cleanNumericString);
                    });
                    priceInput.addEventListener('focus', function() {
                        // When focused, temporarily remove "ریال" for easier editing
                        this.value = this.value.replace(/\s*ریال\s*$/, '').trim();
                    });
                    priceInput.addEventListener('blur', function() {
                        // When losing focus, reapply the formatting
                        this.value = formatPrice(this.value);
                    });
                    priceCell.appendChild(priceInput);

                    const approvalCell = document.createElement('td');

                    // Create toggle wrapper without labels
                    const toggleWrapper = document.createElement('div');
                    toggleWrapper.className = 'toggle-wrapper';

                    // Toggle button
                    const toggleBtn = document.createElement('div');
                    toggleBtn.className = 'toggle-btn';

                    // Add click event
                    toggleBtn.addEventListener('click', function() {
                        this.classList.toggle('active');
                    });

                    // Assemble the wrapper
                    toggleWrapper.appendChild(toggleBtn);

                    approvalCell.appendChild(toggleWrapper);

                    row.appendChild(rowNumCell);
                    row.appendChild(nameCell);
                    row.appendChild(priceCell);
                    row.appendChild(approvalCell);

                    companiesTableBody.appendChild(row);
                });

                if (suppliers.length === 0) {
                    const row = document.createElement('tr');
                    const cell = document.createElement('td');
                    cell.colSpan = 4;
                    cell.textContent = 'هیچ تامین کننده‌ای یافت نشد.';
                    cell.style.textAlign = 'center';
                    row.appendChild(cell);
                    companiesTableBody.appendChild(row);
                }

                // Show the table
                companiesTable.classList.remove('hidden');

                // Populate the dropdown (it's already visible)
                populateRecommendationDropdown();

                // Show success message after processing
                showSnackbar('اطلاعات با موفقیت استخراج شد', 'success');
            });

            // Load settings on page load
            loadSettings();

            // Populate recommendation dropdown on page load
            populateRecommendationDropdown();

            // Add window resize listener to readjust font size
            window.addEventListener('resize', function() {
                if (recommendationBtn.dataset.selectedIndex >= 0) {
                    const selectedIndex = parseInt(recommendationBtn.dataset.selectedIndex);
                    if (recommendations[selectedIndex]) {
                        setTimeout(() => {
                            adjustButtonFontSize(recommendationBtn, recommendations[selectedIndex].title);
                        }, 100);
                    }
                }
            });

            // Event listener for the new "Create Form" button
            createFormBtn.addEventListener('click', function() {
                // Validation: Check if inquiry count and expert recommendation are selected
                const inquiryCountValue = document.getElementById('inquiryCount').value;
                const selectedRecommendationIndex = parseInt(recommendationBtn.dataset.selectedIndex);

                // Check if inquiry count is selected
                if (!inquiryCountValue || inquiryCountValue.trim() === '') {
                    showSnackbar('لطفا دفعات استعلام‌گیری را انتخاب کنید', 'warning');
                    return;
                }

                // Check if expert recommendation is selected
                if (selectedRecommendationIndex < 0 || isNaN(selectedRecommendationIndex)) {
                    showSnackbar('لطفا پیشنهاد کارشناس خرید را انتخاب کنید', 'warning');
                    return;
                }

                // 1. Get the purchase request number
                let purchaseRequestNumber = document.getElementById('refPurchaseNumber').value;

                // 2. Convert Persian/Arabic numbers to English and keep only digits
                if (purchaseRequestNumber) {
                    let englishNumericString = convertPersianToEnglishNumbers(purchaseRequestNumber);
                    purchaseRequestNumber = englishNumericString.replace(/[^0-9]/g, '');
                }

                // 3. Store it in localStorage
                localStorage.setItem('formData_A1', purchaseRequestNumber);

                // 4. Get transaction limit
                let transactionLimitValue = document.getElementById('transactionLimit').value;
                let cleanedTransactionLimit = '-';
                if (transactionLimitValue) {
                    cleanedTransactionLimit = transactionLimitValue.replace(/\s*ریال\s*$/, '').trim();
                    cleanedTransactionLimit = convertPersianToEnglishNumbers(cleanedTransactionLimit);
                    cleanedTransactionLimit = cleanedTransactionLimit.replace(/,/g, '');
                }
                localStorage.setItem('formData_transactionLimit', cleanedTransactionLimit);

                // Store officials information from settings
                const settings = JSON.parse(localStorage.getItem('appSettings') || '{}');
                localStorage.setItem('formData_A4', settings.purchasingExpert || '');
                localStorage.setItem('formData_A5', settings.purchasingManager || '');
                localStorage.setItem('formData_A6', settings.goodsUnitManager || '');

                // Store selected recommendation text for A3 field
                if (selectedRecommendationIndex >= 0 && selectedRecommendationIndex < recommendations.length) {
                    const selectedRecommendation = recommendations[selectedRecommendationIndex];
                    localStorage.setItem('formData_A3', selectedRecommendation.text || '');
                } else {
                    localStorage.setItem('formData_A3', '');
                }

                // 5. Get approved and unapproved suppliers data
                const approvedSuppliersData = [];
                const unapprovedSuppliersData = []; // Changed to store objects {name, price}
                const rows = companiesTableBody.getElementsByTagName('tr');
                for (let i = 0; i < rows.length; i++) {
                    const row = rows[i];
                    const toggleButton = row.querySelector('.toggle-btn');

                    if (row.cells.length > 2 && toggleButton) {
                        const supplierName = row.cells[1].textContent.trim();
                        const priceInput = row.cells[2].querySelector('.editable-input');
                        let rawPrice = '-';
                        if (priceInput) {
                            rawPrice = priceInput.value.replace(/\s*ریال\s*$/, '').trim();
                            rawPrice = convertPersianToEnglishNumbers(rawPrice);
                            rawPrice = rawPrice.replace(/,/g, '');
                        }

                        if (toggleButton.classList.contains('active')) {
                            approvedSuppliersData.push({ name: supplierName, price: rawPrice });
                        } else {
                            unapprovedSuppliersData.push({ name: supplierName, price: rawPrice }); // Store name and price for unapproved
                        }
                    }
                }
                localStorage.setItem('formData_approvedSuppliersData', JSON.stringify(approvedSuppliersData));
                localStorage.removeItem('formData_approvedSuppliers');
                localStorage.setItem('formData_unapprovedSuppliersData', JSON.stringify(unapprovedSuppliersData)); // New key for unapproved data
                localStorage.removeItem('formData_unapprovedSuppliers'); // Remove old key for unapproved names

                // 6. Create A2 template text with the actual values
                const needNumberValue = document.getElementById('needNumber').value;
                const receivedItemsCountValue = document.getElementById('receivedItemsCount').value;

                // Convert Persian numeric inquiry count to Persian ordinal word
                function convertInquiryCountToOrdinal(count) {
                    // Convert to English number first for processing
                    const englishCount = convertPersianToEnglishNumbers(count);

                    // Map of Persian numbers to ordinal words
                    const ordinalMap = {
                        '1': 'اولین',
                        '2': 'دومین',
                        '3': 'سومین',
                        '4': 'چهارمین',
                        '5': 'پنجمین',
                        '6': 'ششمین',
                        '7': 'هفتمین',
                        '8': 'هشتمین',
                        '9': 'نهمین',
                        '10': 'دهمین'
                    };

                    return ordinalMap[englishCount] || count; // Return the ordinal word or the original count if not found
                }

                const inquiryOrdinal = convertInquiryCountToOrdinal(inquiryCountValue);

                // Check if received items count is zero or '-'
                const isZeroItems = receivedItemsCountValue === '۰' ||
                                    receivedItemsCountValue === '0' ||
                                    receivedItemsCountValue === '-' ||
                                    receivedItemsCountValue.trim() === '';

                let a2TemplateText;

                if (isZeroItems) {
                    // Template for zero items received
                    a2TemplateText = `عطف به تقاضای فوق‌الذکر، به استحضار می‌رساند در <b>${inquiryOrdinal}</b> نوبت استعلام‌گیری به عمل آمده در سامانه ستاد دولت با شماره نیاز <b>${needNumberValue}</b>، پاسخی از تولید یا تامین کنندگان دریافت نگردید.`;
                } else {
                    // Template for items received
                    a2TemplateText = `عطف به تقاضای فوق‌الذکر، به استحضار می‌رساند در <b>${inquiryOrdinal}</b> نوبت استعلام‌گیری به عمل آمده در سامانه ستاد دولت با شماره نیاز <b>${needNumberValue}</b> تعداد <b>${receivedItemsCountValue}</b> فقره پیشنهاد از شرکت‌های زیر اخذ شد که پس از ارزیابی این پیشنهادها نتیجه به شرح زیر اعلام می‌گردد:`;
                }

                localStorage.setItem('formData_A2', a2TemplateText);

                // 7. Open form.html in a new tab
                window.open('form.html', '_blank');

                console.log('Relevant data including transaction limit, approved/unapproved supplier data stored. Form.html opened.');

                // Show success message
                showSnackbar('فرم ارزیابی با موفقیت ایجاد شد', 'success');
            });
        });

        // Modern Snackbar function for showing messages with icons
        function showSnackbar(message, type = 'info', duration = 3000) {
            const snackbar = document.getElementById('snackbar');

            // Define icons for different message types
            const icons = {
                success: '✓',
                error: '✕',
                info: 'ℹ',
                warning: '⚠'
            };

            // Clear previous classes
            snackbar.className = 'snackbar';

            // Create the snackbar content with icon and message
            snackbar.innerHTML = `
                <div class="snackbar-icon">${icons[type] || icons.info}</div>
                <div class="snackbar-message">${message}</div>
            `;

            // Add type class (success, error, info, warning)
            snackbar.classList.add(type);

            // Show the snackbar with animation
            snackbar.classList.add('show');

            // Auto-hide functionality
            let hideTimeout = setTimeout(function() {
                snackbar.classList.remove('show');
            }, duration);

            // Add click to dismiss functionality
            snackbar.onclick = function() {
                clearTimeout(hideTimeout);
                snackbar.classList.remove('show');
            };

            // Pause auto-hide on hover
            snackbar.onmouseenter = function() {
                clearTimeout(hideTimeout);
            };

            // Resume auto-hide when mouse leaves
            snackbar.onmouseleave = function() {
                hideTimeout = setTimeout(function() {
                    snackbar.classList.remove('show');
                }, 1000); // Shorter duration after hover
            };
        }
    </script>
</body>
</html> <!-- New version -->
